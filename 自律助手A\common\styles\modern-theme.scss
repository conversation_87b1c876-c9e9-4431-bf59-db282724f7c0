/* 现代化主题样式文件 */
/* 用于统一所有页面的设计风格 */

/* ==================== 颜色变量 ==================== */
:root {
  /* 主色调 */
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);

  /* 功能色彩 */
  --plan-gradient: linear-gradient(135deg, #6366f1, #8b5cf6);
  --checkin-gradient: linear-gradient(135deg, #10b981, #059669);
  --monitor-gradient: linear-gradient(135deg, #f59e0b, #d97706);
  --achievement-gradient: linear-gradient(135deg, #ef4444, #dc2626);
  --social-gradient: linear-gradient(135deg, #06b6d4, #0891b2);
  --profile-gradient: linear-gradient(135deg, #8b5cf6, #a855f7);

  /* 中性色 */
  --white: #ffffff;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* 阴影 */
  --shadow-sm: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4rpx 6rpx rgba(0, 0, 0, 0.07), 0 2rpx 4rpx rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10rpx 15rpx rgba(0, 0, 0, 0.1), 0 4rpx 6rpx rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20rpx 25rpx rgba(0, 0, 0, 0.1), 0 8rpx 10rpx rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25rpx 50rpx rgba(0, 0, 0, 0.25);

  /* 圆角 */
  --radius-sm: 8rpx;
  --radius-md: 12rpx;
  --radius-lg: 16rpx;
  --radius-xl: 20rpx;
  --radius-2xl: 24rpx;
  --radius-full: 50%;
}

/* ==================== 基础页面样式 ==================== */
.modern-page {
  min-height: 100vh;
  background: var(--primary-gradient);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
  }
}

/* ==================== 头部样式 ==================== */
.modern-header {
  padding: 60rpx 40rpx 40rpx;
  position: relative;
  z-index: 1;

  .header-title {
    display: block;
    font-size: 48rpx;
    font-weight: 700;
    color: var(--white);
    margin-bottom: 12rpx;
    text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  }

  .header-subtitle {
    display: block;
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 400;
  }

  .header-decoration {
    position: absolute;
    top: 40rpx;
    right: 40rpx;
    width: 80rpx;
    height: 80rpx;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-full);
    backdrop-filter: blur(10rpx);
  }
}

/* ==================== 内容区域样式 ==================== */
.modern-content {
  padding: 0 30rpx 40rpx;
  position: relative;
  z-index: 1;
}

/* ==================== 卡片样式 ==================== */
.modern-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: var(--radius-xl);
  padding: 32rpx 24rpx;
  margin-bottom: 24rpx;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow: var(--shadow-lg);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.1) 100%);
    pointer-events: none;
  }

  &:active {
    transform: translateY(4rpx);
    box-shadow: var(--shadow-md);
  }
}

/* ==================== 功能网格 ==================== */
.modern-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;

  &.single-column {
    grid-template-columns: 1fr;
  }

  &.three-column {
    grid-template-columns: repeat(3, 1fr);
    gap: 16rpx;
  }
}

/* ==================== 功能卡片 ==================== */
.feature-card {
  @extend .modern-card;

  .card-icon {
    width: 80rpx;
    height: 80rpx;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20rpx;

    .icon-text {
      font-size: 40rpx;
      color: var(--white);
      font-weight: normal;
    }
  }

  .card-content {
    margin-bottom: 16rpx;

    .card-title {
      display: block;
      font-size: 28rpx;
      font-weight: 600;
      color: var(--gray-800);
      margin-bottom: 8rpx;
    }

    .card-desc {
      display: block;
      font-size: 22rpx;
      color: var(--gray-500);
      line-height: 1.4;
    }
  }

  .card-arrow {
    position: absolute;
    top: 24rpx;
    right: 24rpx;

    .arrow-icon {
      font-size: 24rpx;
      color: var(--gray-400);
      font-weight: 600;
    }
  }
}

/* ==================== 主题色卡片 ==================== */
.plan-card .card-icon { background: var(--plan-gradient); }
.checkin-card .card-icon { background: var(--checkin-gradient); }
.monitor-card .card-icon { background: var(--monitor-gradient); }
.achievement-card .card-icon { background: var(--achievement-gradient); }
.social-card .card-icon { background: var(--social-gradient); }
.profile-card .card-icon { background: var(--profile-gradient); }

/* ==================== 按钮样式 ==================== */
.modern-button {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
  border: none;
  border-radius: var(--radius-lg);
  padding: 24rpx 32rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: var(--gray-800);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-md);

  &:active {
    transform: translateY(2rpx);
    box-shadow: var(--shadow-sm);
  }

  &.primary {
    background: var(--plan-gradient);
    color: var(--white);
  }

  &.success {
    background: var(--checkin-gradient);
    color: var(--white);
  }

  &.warning {
    background: var(--monitor-gradient);
    color: var(--white);
  }

  &.danger {
    background: var(--achievement-gradient);
    color: var(--white);
  }
}

/* ==================== 响应式适配 ==================== */
@media (max-width: 750rpx) {
  .modern-grid {
    grid-template-columns: 1fr;
    gap: 20rpx;

    &.three-column {
      grid-template-columns: 1fr 1fr;
    }
  }

  .feature-card {
    padding: 28rpx 20rpx;
  }

  .modern-header {
    padding: 40rpx 30rpx 30rpx;

    .header-title {
      font-size: 40rpx;
    }
  }
}

/* ==================== 动画效果 ==================== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* ==================== 导航栏样式 ==================== */
.modern-navbar {
  background: var(--primary-gradient);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10rpx);
    pointer-events: none;
  }

  .navbar-title {
    color: var(--white);
    font-weight: 600;
    text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
  }
}

/* ==================== TabBar样式增强 ==================== */
.modern-tabbar-page {
  padding-bottom: 100rpx; /* 为自定义TabBar预留空间 */
}

/* ==================== 工具类 ==================== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 8rpx; }
.mb-2 { margin-bottom: 16rpx; }
.mb-3 { margin-bottom: 24rpx; }
.mb-4 { margin-bottom: 32rpx; }

.p-0 { padding: 0; }
.p-1 { padding: 8rpx; }
.p-2 { padding: 16rpx; }
.p-3 { padding: 24rpx; }
.p-4 { padding: 32rpx; }
