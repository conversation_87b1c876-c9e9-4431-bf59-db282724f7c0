<template>
  <view class="modern-page">
    <!-- 头部区域 -->
    <view class="modern-header">
      <text class="header-title">激励语录</text>
      <text class="header-subtitle">每日正能量，点亮学习之路</text>
      <view class="header-decoration"></view>
    </view>

    <!-- 内容区域 -->
    <view class="modern-content">
      <!-- 当前语录卡片 -->
      <view class="modern-card current-saying-card fade-in-up">
        <view class="quote-icon">
          <text class="icon-text">💡</text>
        </view>
        <text class="saying-text">"{{ currentSaying.text }}"</text>
        <text class="saying-author">—— {{ currentSaying.author }}</text>

        <!-- 操作按钮 -->
        <view class="action-buttons">
          <button class="modern-button primary next-btn" @click="nextSaying">
            <text class="btn-icon">🔄</text>
            <text class="btn-text">下一条</text>
          </button>
          <button
            class="modern-button favorite-btn"
            :class="{ 'success': !isFavorite, 'warning': isFavorite }"
            @click="toggleFavorite"
          >
            <text class="btn-icon">{{ isFavorite ? '💖' : '🤍' }}</text>
            <text class="btn-text">{{ isFavorite ? '取消收藏' : '收藏' }}</text>
          </button>
        </view>
      </view>

      <!-- 收藏列表卡片 -->
      <view class="modern-card favorites-card fade-in-up">
        <view class="card-header">
          <view class="header-icon">
            <text class="icon-text">💖</text>
          </view>
          <text class="card-title">我的收藏</text>
          <text class="card-subtitle">{{ favorites.length }}条语录</text>
        </view>

        <view class="favorites-list" v-if="favorites.length > 0">
          <view class="favorite-item" v-for="(item, index) in favorites" :key="index">
            <view class="quote-content">
              <text class="favorite-text">"{{ item.text }}"</text>
              <text class="favorite-author">—— {{ item.author }}</text>
            </view>
            <button class="modern-button danger remove-btn" @click="removeFavorite(index)">
              <text class="btn-icon">🗑️</text>
            </button>
          </view>
        </view>

        <view class="no-favorites" v-if="favorites.length === 0">
          <view class="empty-icon">
            <text class="icon-text">📝</text>
          </view>
          <text class="empty-text">暂无收藏语录</text>
          <text class="empty-desc">收藏喜欢的语录，随时查看</text>
        </view>
      </view>
    </view>

    <!-- 底部装饰 -->
    <view class="bottom-decoration"></view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 语录数据（20条）
const sayings = ref([
  { text: '不积跬步，无以至千里', author: '荀子' },
  { text: '成功的秘诀在于坚持', author: '爱迪生' },
  { text: '每一次努力，都是在靠近梦想', author: '佚名' },
  { text: '只要路是对的，就不怕路远', author: '佚名' },
  { text: '今天的努力，是为了明天的自由', author: '佚名' },
  { text: '失败是成功之母', author: '谚语' },
  { text: '行动是成功的阶梯', author: '佚名' },
  { text: '相信自己，你比想象中更强大', author: '佚名' },
  { text: '时间是最好的老师', author: '佚名' },
  { text: '不怕慢，只怕站', author: '谚语' },
  { text: '努力不一定成功，但放弃一定失败', author: '佚名' },
  { text: '生活不会辜负每一个努力的人', author: '佚名' },
  { text: '坚持到底，就是胜利', author: '佚名' },
  { text: '每一步都在塑造更好的自己', author: '佚名' },
  { text: '成功属于那些不轻言放弃的人', author: '佚名' },
  { text: '用汗水浇灌梦想', author: '佚名' },
  { text: '没有天生的强者，只有不懈的努力', author: '佚名' },
  { text: '勇敢追梦，未来可期', author: '佚名' },
  { text: '学习是改变命运的钥匙', author: '佚名' },
  { text: '每一天都是新的开始', author: '佚名' },
]);

// 当前语录
const currentSaying = ref({ text: '', author: '' });
const isFavorite = ref(false);

// 收藏列表
const favorites = ref([]);

// 加载收藏数据
const loadData = () => {
  try {
    const storedFavorites = uni.getStorageSync('favorites') || '[]';
    favorites.value = JSON.parse(storedFavorites);
    // 随机选择一条语录作为初始显示
    setRandomSaying();
  } catch (e) {
    console.error('加载收藏数据失败', e);
    favorites.value = [];
    setRandomSaying();
  }
};

// 保存收藏数据
const saveData = () => {
  try {
    uni.setStorageSync('favorites', JSON.stringify(favorites.value));
  } catch (e) {
    console.error('保存收藏数据失败', e);
  }
};

// 随机选择一条语录
const setRandomSaying = () => {
  const randomIndex = Math.floor(Math.random() * sayings.value.length);
  currentSaying.value = sayings.value[randomIndex];
  isFavorite.value = favorites.value.some(f => f.text === currentSaying.value.text);
};

// 下一条语录（随机）
const nextSaying = () => {
  setRandomSaying();
};

// 切换收藏状态
const toggleFavorite = () => {
  if (isFavorite.value) {
    const index = favorites.value.findIndex(f => f.text === currentSaying.value.text);
    if (index !== -1) favorites.value.splice(index, 1);
  } else {
    favorites.value.push({ ...currentSaying.value });
  }
  isFavorite.value = !isFavorite.value;
  saveData();
};

// 移除收藏
const removeFavorite = (index) => {
  favorites.value.splice(index, 1);
  isFavorite.value = favorites.value.some(f => f.text === currentSaying.value.text);
  saveData();
};

// 组件挂载时加载数据
onMounted(() => {
  loadData();
});
</script>

<style lang="scss">
// 使用全局现代化主题样式
.bottom-decoration {
  height: 100rpx;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.1), transparent);
  position: relative;
  z-index: 1;
}

// 当前语录卡片样式
.current-saying-card {
  text-align: center;
  padding: 40rpx 32rpx;
  margin-bottom: 24rpx;

  .quote-icon {
    width: 80rpx;
    height: 80rpx;
    margin: 0 auto 24rpx;
    background: var(--checkin-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    .icon-text {
      font-size: 40rpx;
      color: white;
    }
  }

  .saying-text {
    font-size: 32rpx;
    color: var(--gray-800);
    display: block;
    margin-bottom: 20rpx;
    line-height: 1.6;
    font-weight: 500;
    font-style: italic;
  }

  .saying-author {
    font-size: 24rpx;
    color: var(--gray-500);
    display: block;
    margin-bottom: 32rpx;
    font-weight: 500;
  }

  .action-buttons {
    display: flex;
    gap: 16rpx;

    .next-btn, .favorite-btn {
      flex: 1;
      height: 80rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8rpx;

      .btn-icon {
        font-size: 20rpx;
      }

      .btn-text {
        font-size: 26rpx;
        font-weight: 500;
      }
    }
  }
}

// 收藏列表卡片样式
.favorites-card {
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24rpx;

    .header-icon {
      width: 50rpx;
      height: 50rpx;
      border-radius: 50%;
      background: var(--achievement-gradient);
      display: flex;
      align-items: center;
      justify-content: center;

      .icon-text {
        font-size: 20rpx;
        color: white;
      }
    }

    .card-title {
      font-size: 28rpx;
      font-weight: 600;
      color: var(--gray-800);
      flex: 1;
      margin-left: 16rpx;
    }

    .card-subtitle {
      font-size: 22rpx;
      color: var(--gray-500);
    }
  }

  .favorites-list {
    .favorite-item {
      display: flex;
      align-items: center;
      padding: 20rpx 0;
      border-bottom: 1rpx solid var(--gray-200);

      &:last-child {
        border-bottom: none;
      }

      .quote-content {
        flex: 1;
        margin-right: 16rpx;

        .favorite-text {
          font-size: 26rpx;
          color: var(--gray-800);
          display: block;
          margin-bottom: 8rpx;
          line-height: 1.4;
          font-style: italic;
        }

        .favorite-author {
          font-size: 22rpx;
          color: var(--gray-500);
          display: block;
        }
      }

      .remove-btn {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        .btn-icon {
          font-size: 18rpx;
        }
      }
    }
  }

  .no-favorites {
    text-align: center;
    padding: 60rpx 20rpx;

    .empty-icon {
      width: 100rpx;
      height: 100rpx;
      margin: 0 auto 20rpx;
      background: var(--gray-200);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      .icon-text {
        font-size: 50rpx;
        color: var(--gray-400);
      }
    }

    .empty-text {
      display: block;
      font-size: 26rpx;
      font-weight: 500;
      color: var(--gray-600);
      margin-bottom: 8rpx;
    }

    .empty-desc {
      display: block;
      font-size: 22rpx;
      color: var(--gray-400);
    }
  }
}
</style>