/* 安卓设备兼容性样式 */
/* 专门解决安卓设备上的显示问题 */

/* ==================== TabBar 安卓兼容性修复 ==================== */

/* 确保TabBar在安卓设备上正确显示 */
.uni-app--showlayout + .uni-tabbar,
.uni-tabbar {
  background-color: #ffffff !important;
  border-top: 1px solid #e5e5e5 !important;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1) !important;
  height: 50px !important;
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 1000 !important;
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* TabBar项目容器 */
.uni-tabbar .uni-tabbar__item {
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  height: 50px !important;
  padding: 4px 0 !important;
  position: relative !important;
  background: transparent !important;
  border: none !important;
  outline: none !important;
}

/* TabBar图标容器 */
.uni-tabbar .uni-tabbar__icon {
  width: 24px !important;
  height: 24px !important;
  margin-bottom: 2px !important;
  display: block !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
}

/* TabBar图标图片 */
.uni-tabbar .uni-tabbar__icon img {
  width: 100% !important;
  height: 100% !important;
  display: block !important;
}

/* TabBar文字 */
.uni-tabbar .uni-tabbar__text {
  font-size: 10px !important;
  line-height: 1 !important;
  color: #8a8a8a !important;
  text-align: center !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  max-width: 100% !important;
}

/* TabBar选中状态 */
.uni-tabbar .uni-tabbar__item.uni-tabbar__item--selected .uni-tabbar__text {
  color: #667eea !important;
}

/* 安卓特定的TabBar修复 */
@media screen and (max-width: 768px) {
  /* 移动设备特定样式 */
  .uni-tabbar {
    -webkit-transform: translateZ(0) !important;
    transform: translateZ(0) !important;
    -webkit-backface-visibility: hidden !important;
    backface-visibility: hidden !important;
    will-change: transform !important;
  }
  
  .uni-tabbar .uni-tabbar__item {
    -webkit-tap-highlight-color: transparent !important;
    tap-highlight-color: transparent !important;
  }
}

/* ==================== 页面底部间距调整 ==================== */

/* 为TabBar页面添加底部间距 */
.uni-page-wrapper {
  padding-bottom: 50px !important;
}

/* 确保页面内容不被TabBar遮挡 */
page {
  padding-bottom: 50px !important;
}

/* ==================== 安卓WebView特定修复 ==================== */

/* 修复安卓WebView中的渲染问题 */
.uni-tabbar * {
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
  text-rendering: optimizeLegibility !important;
}

/* 强制硬件加速 */
.uni-tabbar {
  -webkit-transform: translate3d(0, 0, 0) !important;
  transform: translate3d(0, 0, 0) !important;
  -webkit-perspective: 1000 !important;
  perspective: 1000 !important;
}

/* ==================== 图标加载优化 ==================== */

/* 确保图标正确加载 */
.uni-tabbar .uni-tabbar__icon {
  background-color: transparent !important;
  border: none !important;
  outline: none !important;
}

/* 图标加载失败时的备用样式 */
.uni-tabbar .uni-tabbar__icon::before {
  content: '' !important;
  display: block !important;
  width: 100% !important;
  height: 100% !important;
  background: transparent !important;
}

/* ==================== 触摸反馈优化 ==================== */

/* 优化触摸反馈 */
.uni-tabbar .uni-tabbar__item:active {
  background-color: rgba(102, 126, 234, 0.1) !important;
  transform: scale(0.95) !important;
  transition: all 0.1s ease !important;
}

/* 移除默认的触摸高亮 */
.uni-tabbar .uni-tabbar__item {
  -webkit-tap-highlight-color: transparent !important;
  -webkit-touch-callout: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
}

/* ==================== 调试样式（可选） ==================== */

/* 调试时可以启用这些样式来检查TabBar是否正确渲染 */
/*
.uni-tabbar {
  border: 2px solid red !important;
}

.uni-tabbar .uni-tabbar__item {
  border: 1px solid blue !important;
}

.uni-tabbar .uni-tabbar__icon {
  border: 1px solid green !important;
}

.uni-tabbar .uni-tabbar__text {
  border: 1px solid orange !important;
}
*/
