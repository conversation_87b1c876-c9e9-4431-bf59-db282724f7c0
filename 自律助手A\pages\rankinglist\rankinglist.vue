<template>
  <view class="modern-page">
    <!-- 头部区域 -->
    <view class="modern-header">
      <text class="header-title">学习排行榜</text>
      <text class="header-subtitle">与伙伴一起进步，见证彼此成长</text>
      <view class="header-decoration" @click="showInfo">
        <text class="help-icon">?</text>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="modern-content">
      <!-- 排行榜卡片 -->
      <view class="modern-card ranking-card fade-in-up">
        <view class="card-header">
          <view class="header-icon">
            <text class="icon-text">🏅</text>
          </view>
          <text class="card-title">连续打卡排行榜</text>
          <text class="card-subtitle">{{ rankingList.length }}位学习者</text>
        </view>

        <view class="ranking-list" v-if="rankingList.length > 0">
          <view class="ranking-item" v-for="(user, index) in rankingList" :key="index">
            <view class="rank-badge" :class="getRankClass(index)">
              <text class="rank-number">{{ index + 1 }}</text>
              <text class="rank-icon" v-if="index < 3">{{ getRankIcon(index) }}</text>
            </view>

            <view class="user-avatar">
              <image :src="user.avatar" mode="aspectFill" class="avatar-image"></image>
            </view>

            <view class="user-info">
              <view class="user-name-row">
                <text class="user-flag">{{ user.flag }}</text>
                <text class="user-name">{{ user.name }}</text>
              </view>
              <text class="user-score">连续打卡 {{ user.streakDays }} 天</text>
            </view>

            <view class="score-display">
              <text class="score-number">{{ user.streakDays }}</text>
              <text class="score-unit">天</text>
            </view>
          </view>
        </view>

        <view class="no-ranking" v-if="rankingList.length === 0">
          <view class="empty-icon">
            <text class="icon-text">🏆</text>
          </view>
          <text class="empty-text">暂无排行数据</text>
          <text class="empty-desc">开始打卡，加入排行榜吧！</text>
        </view>
      </view>
    </view>

    <!-- 底部装饰 -->
    <view class="bottom-decoration"></view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 排行榜数据
import { apiRequest } from '@/utils/request.js';
const rankingList = ref([]);

// 加载排行榜数据（API）
const loadData = async () => {
  try {
    const res = await apiRequest({ url: '/api/rankings/streak', method: 'GET' });
    rankingList.value = res || [];
  } catch (e) {
    rankingList.value = [];
    uni.showToast({ title: '加载排行榜失败', icon: 'none' });
  }
};

// 计算 Dailypunch.vue 数据
const calculatePunchData = (records) => {
  const sortedRecords = records.sort((a, b) => new Date(b.date) - new Date(a.date));
  let streakDays = 0;
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1);

  let currentDate = sortedRecords.some(r => r.date === formatDate(today) && r.punched)
    ? today
    : sortedRecords.some(r => r.date === formatDate(yesterday) && r.punched)
    ? yesterday
    : null;

  if (currentDate) {
    for (const record of sortedRecords) {
      const recordDate = new Date(record.date);
      if (record.punched && formatDate(recordDate) === formatDate(currentDate)) {
        streakDays++;
        currentDate.setDate(currentDate.getDate() - 1);
      } else if (recordDate < currentDate) {
        break;
      }
    }
  }

  return { streakDays };
};
// 格式化日期
const formatDate = (date) => {
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

// 获取排名样式类
const getRankClass = (index) => {
  if (index === 0) return 'rank-first';
  if (index === 1) return 'rank-second';
  if (index === 2) return 'rank-third';
  return 'rank-normal';
};

// 获取排名图标
const getRankIcon = (index) => {
  if (index === 0) return '👑';
  if (index === 1) return '🥈';
  if (index === 2) return '🥉';
  return '';
};

// 显示用法说明
const showInfo = () => {
  uni.showModal({
    title: '排行榜说明',
    content: '排行榜显示你和好友的连续打卡天数。你的打卡数据来自每日打卡记录，排名按连续打卡天数降序排列。',
    showCancel: false,
    confirmText: '知道了',
  });
};

// 组件挂载时加载数据
onMounted(() => {
  loadData();
});
</script>

<style lang="scss">
// 使用全局现代化主题样式
.bottom-decoration {
  height: 100rpx;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.1), transparent);
  position: relative;
  z-index: 1;
}

// 头部装饰样式
.header-decoration {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &:active {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(0.95);
  }

  .help-icon {
    font-size: 32rpx;
    color: white;
    font-weight: 600;
  }
}

// 排行榜卡片样式
.ranking-card {
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24rpx;

    .header-icon {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      background: var(--social-gradient);
      display: flex;
      align-items: center;
      justify-content: center;

      .icon-text {
        font-size: 24rpx;
        color: white;
      }
    }

    .card-title {
      font-size: 28rpx;
      font-weight: 600;
      color: var(--gray-800);
      flex: 1;
      margin-left: 16rpx;
    }

    .card-subtitle {
      font-size: 22rpx;
      color: var(--gray-500);
    }
  }

  .ranking-list {
    .ranking-item {
      display: flex;
      align-items: center;
      padding: 20rpx 0;
      border-bottom: 1rpx solid var(--gray-200);
      transition: all 0.3s ease;

      &:last-child {
        border-bottom: none;
      }

      &:active {
        background: rgba(255, 255, 255, 0.5);
        border-radius: var(--radius-lg);
      }

      .rank-badge {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20rpx;
        position: relative;

        &.rank-first {
          background: linear-gradient(135deg, #ffd700, #ffed4e);
          box-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.3);
        }

        &.rank-second {
          background: linear-gradient(135deg, #c0c0c0, #e5e5e5);
          box-shadow: 0 4rpx 12rpx rgba(192, 192, 192, 0.3);
        }

        &.rank-third {
          background: linear-gradient(135deg, #cd7f32, #daa520);
          box-shadow: 0 4rpx 12rpx rgba(205, 127, 50, 0.3);
        }

        &.rank-normal {
          background: var(--gray-300);
        }

        .rank-number {
          font-size: 24rpx;
          font-weight: 700;
          color: white;
          position: absolute;
          z-index: 1;
        }

        .rank-icon {
          font-size: 20rpx;
          position: absolute;
          top: -8rpx;
          right: -8rpx;
        }
      }

      .user-avatar {
        margin-right: 20rpx;

        .avatar-image {
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
          border: 3rpx solid rgba(255, 255, 255, 0.8);
          box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
        }
      }

      .user-info {
        flex: 1;

        .user-name-row {
          display: flex;
          align-items: center;
          margin-bottom: 6rpx;

          .user-flag {
            font-size: 24rpx;
            margin-right: 8rpx;
          }

          .user-name {
            font-size: 28rpx;
            font-weight: 600;
            color: var(--gray-800);
          }
        }

        .user-score {
          font-size: 22rpx;
          color: var(--gray-500);
        }
      }

      .score-display {
        text-align: center;

        .score-number {
          display: block;
          font-size: 32rpx;
          font-weight: 700;
          color: var(--social-gradient);
          line-height: 1;
        }

        .score-unit {
          font-size: 18rpx;
          color: var(--gray-500);
          margin-top: 2rpx;
        }
      }
    }
  }

  .no-ranking {
    text-align: center;
    padding: 80rpx 20rpx;

    .empty-icon {
      width: 120rpx;
      height: 120rpx;
      margin: 0 auto 24rpx;
      background: var(--gray-200);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      .icon-text {
        font-size: 60rpx;
        color: var(--gray-400);
      }
    }

    .empty-text {
      display: block;
      font-size: 28rpx;
      font-weight: 500;
      color: var(--gray-600);
      margin-bottom: 8rpx;
    }

    .empty-desc {
      display: block;
      font-size: 22rpx;
      color: var(--gray-400);
    }
  }
}
</style>