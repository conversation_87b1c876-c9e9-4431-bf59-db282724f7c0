if("undefined"==typeof Promise||Promise.prototype.finally||(Promise.prototype.finally=function(e){const a=this.constructor;return this.then((t=>a.resolve(e()).then((()=>t))),(t=>a.resolve(e()).then((()=>{throw t}))))}),"undefined"!=typeof uni&&uni&&uni.requireGlobal){const e=uni.requireGlobal();ArrayBuffer=e.<PERSON>,Int8Array=e.Int8Array,Uint8Array=e.Uint8Array,Uint8ClampedArray=e.Uint8ClampedArray,Int16Array=e.Int16Array,Uint16Array=e.Uint16Array,Int32Array=e.Int32Array,Uint32Array=e.Uint32Array,Float32Array=e.Float32Array,Float64Array=e.Float64Array,BigInt64Array=e.<PERSON>nt64<PERSON>rray,BigUint64Array=e.<PERSON>Uint64Array}uni.restoreGlobal&&uni.restoreGlobal(Vue,weex,plus,setTimeout,clearTimeout,setInterval,clearInterval),function(e){"use strict";const a={__name:"society",setup:a=>(a,t)=>(e.openBlock(),e.createElementBlock("view",{class:"modern-page"},[e.createElementVNode("view",{class:"modern-header"},[e.createElementVNode("text",{class:"header-title"},"学习社区"),e.createElementVNode("text",{class:"header-subtitle"},"与志同道合的伙伴一起成长"),e.createElementVNode("view",{class:"header-decoration"})]),e.createElementVNode("view",{class:"modern-content"},[e.createElementVNode("view",{class:"modern-grid three-column"},[e.createElementVNode("navigator",{url:"/pages/rankinglist/rankinglist",class:"feature-card-nav"},[e.createElementVNode("view",{class:"feature-card achievement-card fade-in-up"},[e.createElementVNode("view",{class:"card-icon"},[e.createElementVNode("text",{class:"icon-text"},"🏆")]),e.createElementVNode("view",{class:"card-content"},[e.createElementVNode("text",{class:"card-title"},"排行榜"),e.createElementVNode("text",{class:"card-desc"},"查看学习排名")]),e.createElementVNode("view",{class:"card-arrow"},[e.createElementVNode("text",{class:"arrow-icon"},"→")])])]),e.createElementVNode("navigator",{url:"/pages/friends/friends",class:"feature-card-nav"},[e.createElementVNode("view",{class:"feature-card social-card fade-in-up"},[e.createElementVNode("view",{class:"card-icon"},[e.createElementVNode("text",{class:"icon-text"},"👥")]),e.createElementVNode("view",{class:"card-content"},[e.createElementVNode("text",{class:"card-title"},"学习好友"),e.createElementVNode("text",{class:"card-desc"},"添加学习伙伴")]),e.createElementVNode("view",{class:"card-arrow"},[e.createElementVNode("text",{class:"arrow-icon"},"→")])])]),e.createElementVNode("navigator",{url:"/pages/social/social",class:"feature-card-nav"},[e.createElementVNode("view",{class:"feature-card checkin-card fade-in-up"},[e.createElementVNode("view",{class:"card-icon"},[e.createElementVNode("text",{class:"icon-text"},"💬")]),e.createElementVNode("view",{class:"card-content"},[e.createElementVNode("text",{class:"card-title"},"学习动态"),e.createElementVNode("text",{class:"card-desc"},"分享学习心得")]),e.createElementVNode("view",{class:"card-arrow"},[e.createElementVNode("text",{class:"arrow-icon"},"→")])])])])]),e.createElementVNode("view",{class:"bottom-decoration"})]))},t={__name:"achievement",setup:a=>(a,t)=>(e.openBlock(),e.createElementBlock("view",{class:"modern-page"},[e.createElementVNode("view",{class:"modern-header"},[e.createElementVNode("text",{class:"header-title"},"成就系统"),e.createElementVNode("text",{class:"header-subtitle"},"记录每一次进步与成长"),e.createElementVNode("view",{class:"header-decoration"})]),e.createElementVNode("view",{class:"modern-content"},[e.createElementVNode("view",{class:"modern-grid three-column"},[e.createElementVNode("navigator",{url:"/pages/evaluate/evaluate",class:"feature-card-nav"},[e.createElementVNode("view",{class:"feature-card achievement-card fade-in-up"},[e.createElementVNode("view",{class:"card-icon"},[e.createElementVNode("text",{class:"icon-text"},"⭐")]),e.createElementVNode("view",{class:"card-content"},[e.createElementVNode("text",{class:"card-title"},"学习评价"),e.createElementVNode("text",{class:"card-desc"},"全面评估表现")]),e.createElementVNode("view",{class:"card-arrow"},[e.createElementVNode("text",{class:"arrow-icon"},"→")])])]),e.createElementVNode("navigator",{url:"/pages/achievementwall/achievementwall",class:"feature-card-nav"},[e.createElementVNode("view",{class:"feature-card plan-card fade-in-up"},[e.createElementVNode("view",{class:"card-icon"},[e.createElementVNode("text",{class:"icon-text"},"🏆")]),e.createElementVNode("view",{class:"card-content"},[e.createElementVNode("text",{class:"card-title"},"成就墙"),e.createElementVNode("text",{class:"card-desc"},"展示荣誉成就")]),e.createElementVNode("view",{class:"card-arrow"},[e.createElementVNode("text",{class:"arrow-icon"},"→")])])]),e.createElementVNode("navigator",{url:"/pages/saying/saying",class:"feature-card-nav"},[e.createElementVNode("view",{class:"feature-card checkin-card fade-in-up"},[e.createElementVNode("view",{class:"card-icon"},[e.createElementVNode("text",{class:"icon-text"},"💡")]),e.createElementVNode("view",{class:"card-content"},[e.createElementVNode("text",{class:"card-title"},"激励语录"),e.createElementVNode("text",{class:"card-desc"},"每日正能量")]),e.createElementVNode("view",{class:"card-arrow"},[e.createElementVNode("text",{class:"arrow-icon"},"→")])])])])]),e.createElementVNode("view",{class:"bottom-decoration"})]))},n={__name:"enter",setup(a){const t=e.ref({avatar:"/common/images/1.png",username:"新用户",country:"中国🇨🇳"}),n=e.ref(!1);e.onMounted((()=>{const e=uni.getStorageSync("loginData");e&&e.isLoggedIn&&(n.value=!0);const a=uni.getStorageSync("userData");a&&(t.value=a)}));const l=e=>{if(!e)return"";const a=e.match(/�.*$/);return a?a[0]:""},o=()=>{uni.showModal({title:"提示",content:"确定要退出登录吗？",success:e=>{e.confirm&&(uni.setStorageSync("loginData",{isLoggedIn:!1}),n.value=!1,uni.showToast({title:"已退出登录",icon:"success"}))}})};return(a,c)=>(e.openBlock(),e.createElementBlock("view",{class:"modern-page"},[e.createElementVNode("view",{class:"modern-header"},[e.createElementVNode("text",{class:"header-title"},"个人中心"),e.createElementVNode("text",{class:"header-subtitle"},"管理您的学习生活"),e.createElementVNode("view",{class:"header-decoration"})]),e.createElementVNode("view",{class:"modern-content"},[n.value?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("view",{key:0,class:"login-section"},[e.createElementVNode("view",{class:"modern-card text-center mb-4"},[e.createElementVNode("view",{class:"login-icon"},[e.createElementVNode("text",{class:"icon-text"},"👋")]),e.createElementVNode("text",{class:"login-title"},"欢迎使用自律助手"),e.createElementVNode("text",{class:"login-desc"},"登录后享受更多个性化功能")]),e.createElementVNode("view",{class:"modern-grid"},[e.createElementVNode("navigator",{url:"/pages/login/login",class:"feature-card-nav"},[e.createElementVNode("view",{class:"feature-card profile-card fade-in-up"},[e.createElementVNode("view",{class:"card-icon"},[e.createElementVNode("text",{class:"icon-text"},"🔑")]),e.createElementVNode("view",{class:"card-content"},[e.createElementVNode("text",{class:"card-title"},"登录注册"),e.createElementVNode("text",{class:"card-desc"},"开启学习之旅")]),e.createElementVNode("view",{class:"card-arrow"},[e.createElementVNode("text",{class:"arrow-icon"},"→")])])]),e.createElementVNode("navigator",{url:"/pages/setting/setting",class:"feature-card-nav"},[e.createElementVNode("view",{class:"feature-card monitor-card fade-in-up"},[e.createElementVNode("view",{class:"card-icon"},[e.createElementVNode("text",{class:"icon-text"},"⚙️")]),e.createElementVNode("view",{class:"card-content"},[e.createElementVNode("text",{class:"card-title"},"应用设置"),e.createElementVNode("text",{class:"card-desc"},"个性化配置")]),e.createElementVNode("view",{class:"card-arrow"},[e.createElementVNode("text",{class:"arrow-icon"},"→")])])])])])),n.value?(e.openBlock(),e.createElementBlock("view",{key:1,class:"user-section"},[e.createElementVNode("view",{class:"modern-card user-profile-card mb-4"},[e.createElementVNode("view",{class:"user-avatar-section"},[e.createElementVNode("image",{class:"user-avatar",src:t.value.avatar,mode:"aspectFill"},null,8,["src"]),e.createElementVNode("view",{class:"user-info"},[e.createElementVNode("view",{class:"username-row"},[e.createElementVNode("text",{class:"user-flag"},e.toDisplayString(l(t.value.country)),1),e.createElementVNode("text",{class:"username"},e.toDisplayString(t.value.username),1)]),e.createElementVNode("text",{class:"user-status"},"学习达人")])])]),e.createElementVNode("view",{class:"modern-grid three-column"},[e.createElementVNode("navigator",{url:"/pages/user/user",class:"feature-card-nav"},[e.createElementVNode("view",{class:"feature-card profile-card fade-in-up"},[e.createElementVNode("view",{class:"card-icon"},[e.createElementVNode("text",{class:"icon-text"},"👤")]),e.createElementVNode("view",{class:"card-content"},[e.createElementVNode("text",{class:"card-title"},"个人资料"),e.createElementVNode("text",{class:"card-desc"},"查看详细信息")]),e.createElementVNode("view",{class:"card-arrow"},[e.createElementVNode("text",{class:"arrow-icon"},"→")])])]),e.createElementVNode("navigator",{url:"/pages/setting/setting",class:"feature-card-nav"},[e.createElementVNode("view",{class:"feature-card monitor-card fade-in-up"},[e.createElementVNode("view",{class:"card-icon"},[e.createElementVNode("text",{class:"icon-text"},"⚙️")]),e.createElementVNode("view",{class:"card-content"},[e.createElementVNode("text",{class:"card-title"},"应用设置"),e.createElementVNode("text",{class:"card-desc"},"个性化配置")]),e.createElementVNode("view",{class:"card-arrow"},[e.createElementVNode("text",{class:"arrow-icon"},"→")])])]),e.createElementVNode("view",{class:"feature-card-nav",onClick:o},[e.createElementVNode("view",{class:"feature-card achievement-card fade-in-up"},[e.createElementVNode("view",{class:"card-icon"},[e.createElementVNode("text",{class:"icon-text"},"🚪")]),e.createElementVNode("view",{class:"card-content"},[e.createElementVNode("text",{class:"card-title"},"退出登录"),e.createElementVNode("text",{class:"card-desc"},"安全退出账户")]),e.createElementVNode("view",{class:"card-arrow"},[e.createElementVNode("text",{class:"arrow-icon"},"→")])])])])])):e.createCommentVNode("",!0)]),e.createElementVNode("view",{class:"bottom-decoration"})]))}},l={__name:"plan",setup:a=>(a,t)=>(e.openBlock(),e.createElementBlock("view",{class:"modern-page"},[e.createElementVNode("view",{class:"modern-header"},[e.createElementVNode("text",{class:"header-title"},"智能计划"),e.createElementVNode("text",{class:"header-subtitle"},"科学规划，高效执行"),e.createElementVNode("view",{class:"header-decoration"})]),e.createElementVNode("view",{class:"modern-content"},[e.createElementVNode("view",{class:"modern-grid"},[e.createElementVNode("navigator",{url:"/pages/planmanagement/planmanagement",class:"feature-card-nav"},[e.createElementVNode("view",{class:"feature-card plan-card fade-in-up"},[e.createElementVNode("view",{class:"card-icon"},[e.createElementVNode("text",{class:"icon-text"},"📋")]),e.createElementVNode("view",{class:"card-content"},[e.createElementVNode("text",{class:"card-title"},"计划管理"),e.createElementVNode("text",{class:"card-desc"},"创建和管理学习计划")]),e.createElementVNode("view",{class:"card-arrow"},[e.createElementVNode("text",{class:"arrow-icon"},"→")])])]),e.createElementVNode("navigator",{url:"/pages/timereminder/timereminder",class:"feature-card-nav"},[e.createElementVNode("view",{class:"feature-card monitor-card fade-in-up"},[e.createElementVNode("view",{class:"card-icon"},[e.createElementVNode("text",{class:"icon-text"},"⏰")]),e.createElementVNode("view",{class:"card-content"},[e.createElementVNode("text",{class:"card-title"},"时间提醒"),e.createElementVNode("text",{class:"card-desc"},"智能提醒，不错过任何计划")]),e.createElementVNode("view",{class:"card-arrow"},[e.createElementVNode("text",{class:"arrow-icon"},"→")])])])])]),e.createElementVNode("view",{class:"bottom-decoration"})]))},o={__name:"daily",setup:a=>(a,t)=>(e.openBlock(),e.createElementBlock("view",{class:"modern-page"},[e.createElementVNode("view",{class:"modern-header"},[e.createElementVNode("text",{class:"header-title"},"每日管理"),e.createElementVNode("text",{class:"header-subtitle"},"坚持每一天，成就更好的自己"),e.createElementVNode("view",{class:"header-decoration"})]),e.createElementVNode("view",{class:"modern-content"},[e.createElementVNode("view",{class:"modern-grid three-column"},[e.createElementVNode("navigator",{url:"/pages/dailypunch/dailypunch",class:"feature-card-nav"},[e.createElementVNode("view",{class:"feature-card checkin-card fade-in-up"},[e.createElementVNode("view",{class:"card-icon"},[e.createElementVNode("text",{class:"icon-text"},"✓")]),e.createElementVNode("view",{class:"card-content"},[e.createElementVNode("text",{class:"card-title"},"每日打卡"),e.createElementVNode("text",{class:"card-desc"},"养成良好习惯")]),e.createElementVNode("view",{class:"card-arrow"},[e.createElementVNode("text",{class:"arrow-icon"},"→")])])]),e.createElementVNode("navigator",{url:"/pages/schedule/schedule",class:"feature-card-nav"},[e.createElementVNode("view",{class:"feature-card plan-card fade-in-up"},[e.createElementVNode("view",{class:"card-icon"},[e.createElementVNode("text",{class:"icon-text"},"📈")]),e.createElementVNode("view",{class:"card-content"},[e.createElementVNode("text",{class:"card-title"},"进度追踪"),e.createElementVNode("text",{class:"card-desc"},"实时查看进度")]),e.createElementVNode("view",{class:"card-arrow"},[e.createElementVNode("text",{class:"arrow-icon"},"→")])])]),e.createElementVNode("navigator",{url:"/pages/dataanalysis/dataanalysis",class:"feature-card-nav"},[e.createElementVNode("view",{class:"feature-card monitor-card fade-in-up"},[e.createElementVNode("view",{class:"card-icon"},[e.createElementVNode("text",{class:"icon-text"},"📊")]),e.createElementVNode("view",{class:"card-content"},[e.createElementVNode("text",{class:"card-title"},"数据分析"),e.createElementVNode("text",{class:"card-desc"},"深度数据洞察")]),e.createElementVNode("view",{class:"card-arrow"},[e.createElementVNode("text",{class:"arrow-icon"},"→")])])])])]),e.createElementVNode("view",{class:"bottom-decoration"})]))};function c(e,a,...t){uni.__log__?uni.__log__(e,a,...t):console[e].apply(console,[...t,a])}async function r(e){try{const a=await async function(e){const a={contents:[{parts:[{text:'请分析这张图片中人物的学习状态，从以下几个维度进行评估：\n\n1. 专注度 (0-100分)：\n   - 眼神是否专注于学习材料\n   - 坐姿是否端正\n   - 是否有分心行为（如玩手机、东张西望等）\n\n2. 疲劳指数 (0-100分)：\n   - 眼部疲劳程度（眼睛是否疲惫、眯眼等）\n   - 面部表情是否显示疲劳\n   - 身体姿态是否显示疲惫\n\n请以JSON格式返回结果，包含以下字段：\n{\n  "focusLevel": 数字(0-100),\n  "fatigueLevel": 数字(0-100),\n  "confidence": 数字(0-100),\n  "description": "简短的状态描述"\n}\n\n只返回JSON，不要其他文字。'},{inline_data:{mime_type:"image/jpeg",data:e}}]}],generationConfig:{temperature:.1,topK:32,topP:1,maxOutputTokens:1024}};try{const e=await uni.request({url:"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=AIzaSyBcTSbJVxQj4_atCBq3UuZxy88HfER1Rog",method:"POST",header:{"Content-Type":"application/json"},data:a,timeout:3e4});if(!(200===e.statusCode&&e.data.candidates&&e.data.candidates.length>0))throw new Error("Gemini API请求失败");{const a=e.data.candidates[0].content.parts[0].text;try{const e=JSON.parse(a.trim());if("number"==typeof e.focusLevel&&"number"==typeof e.fatigueLevel&&"number"==typeof e.confidence)return e.focusLevel=Math.max(0,Math.min(100,e.focusLevel)),e.fatigueLevel=Math.max(0,Math.min(100,e.fatigueLevel)),e.confidence=Math.max(0,Math.min(100,e.confidence)),e;throw new Error("Invalid response format")}catch(t){throw c("error","at utils/geminiService.js:113","Failed to parse Gemini response:",t),new Error("AI响应格式错误")}}}catch(n){throw c("error","at utils/geminiService.js:120","Gemini API Error:",n),n}}(e);return{success:!0,data:a}}catch(a){return c("error","at utils/geminiService.js:173","Study state analysis failed:",a),{success:!1,error:a.message||"图像分析失败",data:{focusLevel:Math.round(100*Math.random()),fatigueLevel:Math.round(100*Math.random()),confidence:0,description:"图像识别失败，使用随机数据"}}}}const s={__name:"monitor",setup(a){const t=e.ref(!1),n=e.ref(0),l=e.ref(0),o=e.ref(0);let s=null;const d=e.ref(!0),i=e.ref(!1),m=e.ref(null),u=e.ref(!1),N=e.ref(null),v=e.ref(null),E=e.ref(null),g=e.ref(!0);let V=null;const p=e.ref([]),w=e.ref([]),h=e.ref(""),f=e.ref([]),x=e.ref(!1),y=()=>{try{uni.setStorageSync("monitorRecords",JSON.stringify(f.value))}catch(e){c("error","at pages/monitor/monitor.vue:202","保存记录失败",e)}},k=()=>navigator.mediaDevices&&navigator.mediaDevices.getUserMedia?(u.value=!0,!0):(u.value=!1,c("warn","at pages/monitor/monitor.vue:213","当前浏览器不支持摄像头功能"),!1),C=()=>{try{if(N.value){N.value.getTracks().forEach((e=>{try{e.stop()}catch(a){c("warn","at pages/monitor/monitor.vue:335","停止track失败:",a)}})),N.value=null,c("log","at pages/monitor/monitor.vue:339","摄像头已停止")}if(v.value)try{v.value.srcObject=null}catch(e){c("warn","at pages/monitor/monitor.vue:347","清理video元素失败:",e)}}catch(e){c("error","at pages/monitor/monitor.vue:351","停止摄像头时出错:",e)}},S=e=>{g.value=e.detail.value,g.value&&t.value&&N.value?B():D(),uni.showToast({title:g.value?"已开启自动拍照":"已关闭自动拍照",icon:"success"})},b=e=>{d.value=e.detail.value,d.value||(m.value=null),uni.showToast({title:d.value?"已开启AI识别":"已关闭AI识别",icon:"success"})},B=()=>{V&&clearInterval(V),g.value&&d.value&&N.value&&(c("log","at pages/monitor/monitor.vue:392","开始自动拍照，间隔20秒"),V=setInterval((()=>{t.value&&N.value&&!i.value&&(c("log","at pages/monitor/monitor.vue:395","执行自动拍照"),T())}),2e4))},D=()=>{V&&(clearInterval(V),V=null,c("log","at pages/monitor/monitor.vue:407","停止自动拍照"))},T=async()=>{if(!i.value)if(d.value)if(N.value)try{i.value=!0;const e=(async()=>{try{if(!v.value||!E.value)return c("error","at pages/monitor/monitor.vue:415","video或canvas元素未找到"),null;const a=v.value,t=E.value;if(a.readyState<2)return c("error","at pages/monitor/monitor.vue:424","video尚未准备就绪，readyState:",a.readyState),null;const n=t.getContext("2d");if(!n)return c("error","at pages/monitor/monitor.vue:430","无法获取canvas上下文"),null;let l=0;for(;(!a.videoWidth||!a.videoHeight)&&l<20;)await new Promise((e=>setTimeout(e,100))),l++;if(a.videoWidth&&a.videoHeight)t.width=a.videoWidth,t.height=a.videoHeight,n.drawImage(a,0,0,t.width,t.height);else{c("error","at pages/monitor/monitor.vue:443","video尺寸无效，videoWidth:",a.videoWidth,"videoHeight:",a.videoHeight);const l=640,o=480;c("log","at pages/monitor/monitor.vue:447","使用默认尺寸:",l,"x",o),t.width=l,t.height=o;try{n.drawImage(a,0,0,l,o)}catch(e){return c("error","at pages/monitor/monitor.vue:456","绘制失败:",e),null}}const o=t.toDataURL("image/jpeg",.8);return o&&"data:,"!==o?o:(c("error","at pages/monitor/monitor.vue:472","canvas转换失败"),null)}catch(a){return c("error","at pages/monitor/monitor.vue:478","拍照过程出错:",a),null}})();if(!e)throw new Error("拍照失败");const a=await(async e=>{try{return e.replace(/^data:image\/jpeg;base64,/,"")}catch(a){throw c("error","at pages/monitor/monitor.vue:493","base64转换失败:",a),a}})(e),t=await r(a);t.success?(l.value=Math.round(t.data.focusLevel),o.value=Math.round(t.data.fatigueLevel),m.value=t.data,p.value.length>0&&(p.value[p.value.length-1]=l.value,w.value[w.value.length-1]=o.value),uni.showToast({title:"分析完成",icon:"success"})):(M(),uni.showToast({title:t.error||"分析失败，使用随机数据",icon:"none"}))}catch(e){c("error","at pages/monitor/monitor.vue:556","拍照分析错误:",e),M(),uni.showToast({title:"拍照失败，使用随机数据",icon:"none"})}finally{i.value=!1}else uni.showToast({title:"摄像头未就绪",icon:"none"});else M()},M=()=>{l.value=Math.round(100*Math.random()),o.value=Math.round(100*Math.random()),p.value.length>0&&(p.value[p.value.length-1]=l.value,w.value[w.value.length-1]=o.value)},L=e.computed((()=>{const e=Math.floor(n.value/3600),a=Math.floor(n.value%3600/60),t=n.value%60;return`${String(e).padStart(2,"0")}:${String(a).padStart(2,"0")}:${String(t).padStart(2,"0")}`})),_=async()=>{if(t.value)clearInterval(s),D(),C(),A(),P(),p.value=[],w.value=[],m.value=null,t.value=!1;else{t.value=!0,n.value=0,await e.nextTick(),await new Promise((e=>setTimeout(e,200)));try{await(async()=>{if(!k())return!1;try{const t={video:{width:{ideal:640},height:{ideal:480},facingMode:"user"},audio:!1},n=await navigator.mediaDevices.getUserMedia(t);N.value=n,await e.nextTick(),await new Promise((e=>setTimeout(e,100)));let l=0;const o=10;for(;l<o;){if(await e.nextTick(),v.value)try{const e=v.value;return e.srcObject=n,await new Promise(((a,t)=>{const n=setTimeout((()=>{t(new Error("Video加载超时"))}),5e3),l=()=>{clearTimeout(n),e.removeEventListener("loadeddata",l),e.removeEventListener("error",o),a()},o=a=>{clearTimeout(n),e.removeEventListener("loadeddata",l),e.removeEventListener("error",o),t(a)};e.addEventListener("loadeddata",l),e.addEventListener("error",o),e.readyState>=2&&l()})),c("log","at pages/monitor/monitor.vue:282","H5摄像头初始化成功"),!0}catch(a){c("error","at pages/monitor/monitor.vue:285","设置video srcObject失败:",a),l++,await new Promise((e=>setTimeout(e,100)));continue}l++,await new Promise((e=>setTimeout(e,50)))}return c("error","at pages/monitor/monitor.vue:296","video元素未找到，尝试次数:",l),n&&n.getTracks().forEach((e=>e.stop())),N.value=null,!1}catch(t){c("error","at pages/monitor/monitor.vue:305","摄像头初始化失败:",t),u.value=!1;let e="摄像头启动失败";return"NotAllowedError"===t.name?e="摄像头权限被拒绝，请允许访问摄像头":"NotFoundError"===t.name?e="未找到摄像头设备":"NotReadableError"===t.name&&(e="摄像头被其他应用占用"),uni.showToast({title:e,icon:"none",duration:3e3}),!1}})()?g.value&&d.value&&B():uni.showToast({title:"摄像头初始化失败，将使用随机数据",icon:"none",duration:3e3})}catch(a){c("error","at pages/monitor/monitor.vue:625","摄像头初始化异常:",a),uni.showToast({title:"摄像头初始化异常，将使用随机数据",icon:"none",duration:3e3})}d.value&&N.value?(l.value=0,o.value=0,g.value?uni.showToast({title:"摄像头已启动，将每20秒自动拍照分析",icon:"success",duration:3e3}):uni.showToast({title:"摄像头已启动，请开启自动拍照或手动拍照",icon:"none",duration:3e3})):(l.value=Math.round(100*Math.random()),o.value=Math.round(100*Math.random())),p.value=[l.value],w.value=[o.value],h.value="",s=setInterval((()=>{n.value+=1,n.value%60==0&&(d.value&&N.value&&g.value?(p.value.push(l.value),w.value.push(o.value)):(l.value=Math.round(100*Math.random()),o.value=Math.round(100*Math.random()),p.value.push(l.value),w.value.push(o.value)))}),1e3)}},A=()=>{const e=p.value.length>0?p.value.reduce(((e,a)=>e+a),0)/p.value.length:l.value,a=w.value.length>0?w.value.reduce(((e,a)=>e+a),0)/w.value.length:o.value,t=(e+(100-a))/2;h.value=t<30?`平均专注度 ${e.toFixed(0)}%，疲劳指数 ${a.toFixed(0)}%，学习状态较差，建议休息15分钟并调整学习内容。`:t<50?`平均专注度 ${e.toFixed(0)}%，疲劳指数 ${a.toFixed(0)}%，状态不佳，建议休息10分钟或切换轻松任务。`:t<70?`平均专注度 ${e.toFixed(0)}%，疲劳指数 ${a.toFixed(0)}%，状态一般，建议休息5分钟或调整学习节奏。`:`平均专注度 ${e.toFixed(0)}%，疲劳指数 ${a.toFixed(0)}%，状态良好，请继续保持专注并合理安排时间！`},P=()=>{const e=new Date,a=`${e.getFullYear()}-${String(e.getMonth()+1).padStart(2,"0")}-${String(e.getDate()).padStart(2,"0")} ${String(e.getHours()).padStart(2,"0")}:${String(e.getMinutes()).padStart(2,"0")}:${String(e.getSeconds()).padStart(2,"0")}`;f.value.unshift({time:a,detail:h.value}),y()},G=()=>{uni.showModal({title:"确认清除",content:"确定要清除所有学习行为记录吗？",success:e=>{e.confirm&&(f.value=[],y(),uni.showToast({title:"记录已清除",icon:"success"}))}})},I=()=>{x.value=!0},R=(e,a="")=>{c("error","at pages/monitor/monitor.vue:732",`全局错误 [${a}]:`,e),e.message&&e.message.includes("message port closed")||a.includes("摄像头")&&uni.showToast({title:"摄像头功能异常",icon:"none",duration:2e3})},U=async()=>{try{window.addEventListener("error",(e=>{R(e.error,"全局异常")})),window.addEventListener("unhandledrejection",(e=>{R(e.reason,"Promise异常")})),(()=>{try{const e=uni.getStorageSync("monitorRecords")||"[]";f.value=JSON.parse(e)}catch(e){c("error","at pages/monitor/monitor.vue:192","加载记录失败",e),f.value=[]}})(),k(),c("log","at pages/monitor/monitor.vue:767","应用初始化完成，摄像头支持:",u.value)}catch(e){R(e,"应用初始化")}};return e.onMounted((()=>{U()})),e.onUnmounted((()=>{s&&clearInterval(s),D(),C()})),(a,n)=>(e.openBlock(),e.createElementBlock("view",{class:"modern-page"},[e.createElementVNode("view",{class:"modern-header"},[e.createElementVNode("text",{class:"header-title"},"AI学习监测"),e.createElementVNode("text",{class:"header-subtitle"},"智能分析学习状态，提升专注效率"),e.createElementVNode("view",{class:"header-decoration",onClick:I},[e.createElementVNode("text",{class:"help-icon"},"?")])]),e.createElementVNode("view",{class:"modern-content"},[t.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"modern-card camera-container"},[e.createElementVNode("view",{class:"camera-header"},[e.createElementVNode("text",{class:"camera-title"},"学习状态监测"),e.createElementVNode("text",{class:e.normalizeClass(["camera-status",{analyzing:i.value}])},e.toDisplayString(i.value?"分析中...":d.value?"AI识别模式":"随机模式"),3)]),e.createElementVNode("view",{class:"camera-preview-container"},[u.value&&N.value?(e.openBlock(),e.createElementBlock("video",{key:0,ref_key:"videoElement",ref:v,class:"camera-preview",autoplay:"",muted:"",playsinline:""},null,512)):u.value?(e.openBlock(),e.createElementBlock("view",{key:2,class:"camera-loading"},[e.createElementVNode("text",{class:"loading-text"},"正在启动摄像头...")])):(e.openBlock(),e.createElementBlock("view",{key:1,class:"camera-fallback"},[e.createElementVNode("text",{class:"fallback-text"},"当前浏览器不支持摄像头功能"),e.createElementVNode("text",{class:"fallback-hint"},"请使用支持摄像头的浏览器或移动端应用")])),e.createElementVNode("canvas",{ref_key:"canvasElement",ref:E,class:"capture-canvas",style:{display:"none"}},null,512)]),e.createElementVNode("view",{class:"camera-controls"},[e.createElementVNode("view",{class:"control-group"},[e.createElementVNode("view",{class:"ai-toggle"},[e.createElementVNode("text",{class:"toggle-label"},"AI识别:"),e.createElementVNode("switch",{checked:d.value,onChange:b},null,40,["checked"])]),e.createElementVNode("view",{class:"auto-toggle"},[e.createElementVNode("text",{class:"toggle-label"},"自动拍照:"),e.createElementVNode("switch",{checked:g.value,onChange:S},null,40,["checked"])])]),e.createElementVNode("view",{class:"status-info"},[e.createElementVNode("text",{class:"status-text"},e.toDisplayString(i.value?"正在分析...":g.value&&N.value?"每20秒自动拍照":N.value?"手动模式":"摄像头未就绪"),1)])]),m.value&&void 0!==m.value.confidence?(e.openBlock(),e.createElementBlock("view",{key:0,class:"confidence-info"},[e.createElementVNode("text",{class:"confidence-text"},"识别置信度: "+e.toDisplayString(m.value.confidence)+"%",1),e.createElementVNode("text",{class:"description-text"},e.toDisplayString(m.value.description),1)])):e.createCommentVNode("",!0)])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"modern-card status-container"},[e.createElementVNode("view",{class:"status-header"},[e.createElementVNode("text",{class:"status-title"},"当前学习状态"),e.createElementVNode("text",{class:"status-time"},"监测时长: "+e.toDisplayString(L.value),1)]),e.createElementVNode("view",{class:"status-grid"},[e.createElementVNode("view",{class:"status-item"},[e.createElementVNode("text",{class:"status-label"},"专注度"),e.createElementVNode("text",{class:e.normalizeClass(["status-value",{good:l.value>=70,warning:l.value<70}])},e.toDisplayString(l.value)+"% ",3)]),e.createElementVNode("view",{class:"status-item"},[e.createElementVNode("text",{class:"status-label"},"疲劳指数"),e.createElementVNode("text",{class:e.normalizeClass(["status-value",{good:o.value<50,warning:o.value>=50}])},e.toDisplayString(o.value)+"% ",3)])]),e.createElementVNode("button",{class:"modern-button primary start-button",onClick:_},e.toDisplayString(t.value?"停止监测":"开始监测"),1)]),e.createElementVNode("view",{class:"modern-card suggestion-container"},[e.createElementVNode("view",{class:"section-header"},[e.createElementVNode("text",{class:"section-title"},"AI学习建议")]),h.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"suggestion-content"},[e.createElementVNode("text",{class:"suggestion-text"},e.toDisplayString(h.value),1)])):(e.openBlock(),e.createElementBlock("view",{key:1,class:"no-suggestion"},[e.createElementVNode("text",{class:"placeholder-text"},"停止监测后将提供建议")]))]),e.createElementVNode("view",{class:"modern-card record-container"},[e.createElementVNode("view",{class:"record-header"},[e.createElementVNode("text",{class:"section-title"},"学习行为记录"),e.createElementVNode("button",{class:"modern-button danger clear-button",onClick:G},"清除记录")]),e.createElementVNode("view",{class:"record-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(f.value,((a,t)=>(e.openBlock(),e.createElementBlock("view",{class:"record-item",key:t},[e.createElementVNode("text",{class:"record-time"},e.toDisplayString(a.time),1),e.createElementVNode("text",{class:"record-detail"},e.toDisplayString(a.detail),1)])))),128)),0===f.value.length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"no-records"},[e.createElementVNode("text",{class:"placeholder-text"},"暂无记录")])):e.createCommentVNode("",!0)])])]),e.createElementVNode("view",{class:"bottom-decoration"}),x.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"modal"},[e.createElementVNode("view",{class:"modal-content"},[e.createElementVNode("view",{class:"modal-header"},[e.createElementVNode("text",{class:"modal-title"},"使用方法"),e.createElementVNode("text",{class:"close-button",onClick:n[0]||(n[0]=e=>x.value=!1)},"×")]),e.createElementVNode("view",{class:"modal-body"},[e.createElementVNode("text",{class:"help-text"}," 1. 点击“开始监测”启动学习状态监测，监测时长精确到秒。\\n 2. 每分钟更新一次专注度和疲劳指数。\\n 3. 点击“停止监测”结束并生成基于平均值的建议。\\n 4. 建议会记录到“学习行为记录”中，永久保存。\\n 5. 点击“清除记录”可删除所有记录。\\n 6. 若监测不足1分钟，使用当前数据生成建议。 ")])])])):e.createCommentVNode("",!0)]))}};function d({url:e,method:a="GET",data:t={},header:n={}}){const l=uni.getStorageSync("token")||"";return new Promise(((o,c)=>{uni.request({url:"http://localhost:5000"+e,method:a,data:t,header:{Authorization:l?"Bearer "+l:"","Content-Type":"application/json",...n},success:e=>{200===e.statusCode?o(e.data):(uni.showToast({title:e.data.msg||"请求失败",icon:"none"}),c(e.data))},fail:e=>{uni.showToast({title:"网络错误",icon:"none"}),c(e)}})}))}async function i(){try{const e=uni.getStorageSync("punchRecords");if(e){const a=JSON.parse(e);for(const e of a)e.date&&e.punched&&await d({url:"/api/punches",method:"POST",data:{date:e.date,content:"",type:"每日打卡",created_at:e.date}});uni.removeStorageSync("punchRecords")}}catch(e){c("error","at utils/migrateLocalData.js:17","迁移打卡记录失败",e)}try{const e=uni.getStorageSync("plans");if(e){const a=JSON.parse(e);for(const e of a)e.title&&e.time&&await d({url:"/api/plans",method:"POST",data:{title:e.title,remind_time:e.time,completed:!!e.completed,created_at:e.time}});uni.removeStorageSync("plans")}}catch(e){c("error","at utils/migrateLocalData.js:31","迁移计划失败",e)}try{const e=uni.getStorageSync("tasks");if(e){const a=JSON.parse(e);for(const e of a)e.title&&await d({url:"/api/tasks",method:"POST",data:{title:e.title,description:e.description||"",completed:!!e.completed}});uni.removeStorageSync("tasks")}}catch(e){c("error","at utils/migrateLocalData.js:45","迁移任务失败",e)}try{const e=uni.getStorageSync("profile");if(e){const a=JSON.parse(e);await d({url:"/api/profile",method:"POST",data:a}),uni.removeStorageSync("profile")}}catch(e){c("error","at utils/migrateLocalData.js:55","迁移个人资料失败",e)}}const m={__name:"planmanagement",setup(a){const t=e.ref([]),n=e.ref(!1),l=e.reactive({title:"",date:"",time:""}),o=async()=>{try{const e=await d({url:"/api/plans",method:"GET"});t.value=(e||[]).map((e=>({id:e.id,title:e.title,time:e.remind_time,completed:!!e.completed})))}catch(e){t.value=[]}},c=e=>{l.date=e.detail.value},r=e=>{l.time=e.detail.value},s=async()=>{if(!l.title||!l.date||!l.time)return void uni.showToast({title:"请填写完整信息",icon:"none"});const e=`${l.date} ${l.time}`;try{await d({url:"/api/plans",method:"POST",data:{title:l.title,remind_time:e,completed:!1,created_at:e}}),uni.showToast({title:"添加成功",icon:"success"}),n.value=!1,l.title="",l.date="",l.time="",await o()}catch(a){}};return e.onMounted((async()=>{await i(),await o()})),e.onMounted((()=>{o()})),(a,i)=>(e.openBlock(),e.createElementBlock("view",{class:"modern-page"},[e.createElementVNode("view",{class:"modern-header"},[e.createElementVNode("text",{class:"header-title"},"计划管理"),e.createElementVNode("text",{class:"header-subtitle"},"创建和管理您的学习计划"),e.createElementVNode("view",{class:"header-decoration"})]),e.createElementVNode("view",{class:"modern-content"},[e.createElementVNode("view",{class:"modern-card add-plan-card fade-in-up",onClick:i[0]||(i[0]=e=>n.value=!0)},[e.createElementVNode("view",{class:"add-icon"},[e.createElementVNode("text",{class:"icon-text"},"➕")]),e.createElementVNode("text",{class:"add-title"},"添加新计划"),e.createElementVNode("text",{class:"add-desc"},"制定新的学习目标")]),e.createElementVNode("view",{class:"plans-section"},[t.value.length>0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"section-header"},[e.createElementVNode("text",{class:"section-title"},"我的计划"),e.createElementVNode("text",{class:"section-subtitle"},e.toDisplayString(t.value.length)+"个计划",1)])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"plan-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(t.value,((a,n)=>(e.openBlock(),e.createElementBlock("view",{class:"modern-card plan-item fade-in-up",key:n},[e.createElementVNode("view",{class:"plan-header"},[e.createElementVNode("view",{class:e.normalizeClass(["plan-status-icon",{completed:a.completed}])},[e.createElementVNode("text",{class:"icon-text"},e.toDisplayString(a.completed?"✅":"⏰"),1)],2),e.createElementVNode("view",{class:"plan-info"},[e.createElementVNode("text",{class:"plan-title"},e.toDisplayString(a.title),1),e.createElementVNode("text",{class:"plan-time"},e.toDisplayString(a.time),1)])]),e.createElementVNode("view",{class:e.normalizeClass(["plan-status-badge",{completed:a.completed}])},[e.createElementVNode("text",{class:"status-text"},e.toDisplayString(a.completed?"已完成":"进行中"),1)],2),e.createElementVNode("view",{class:"plan-actions"},[e.createElementVNode("button",{class:e.normalizeClass(["modern-button action-btn",{success:!a.completed,warning:a.completed}]),onClick:e=>(async e=>{const a=t.value[e];if(a)try{await d({url:`/api/plans/${a.id}`,method:"PUT",data:{completed:!a.completed}}),await o()}catch(n){}})(n)},e.toDisplayString(a.completed?"标记未完成":"标记完成"),11,["onClick"]),e.createElementVNode("button",{class:"modern-button danger action-btn",onClick:e=>(async e=>{const a=t.value[e];if(a)try{await d({url:`/api/plans/${a.id}`,method:"DELETE"}),await o()}catch(n){}})(n)}," 删除 ",8,["onClick"])])])))),128)),0===t.value.length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"no-plans"},[e.createElementVNode("view",{class:"empty-icon"},[e.createElementVNode("text",{class:"icon-text"},"📝")]),e.createElementVNode("text",{class:"empty-text"},"暂无计划"),e.createElementVNode("text",{class:"empty-desc"},"点击上方按钮创建您的第一个计划")])):e.createCommentVNode("",!0)])])]),n.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"modern-modal"},[e.createElementVNode("view",{class:"modal-overlay",onClick:i[1]||(i[1]=e=>n.value=!1)}),e.createElementVNode("view",{class:"modal-content"},[e.createElementVNode("view",{class:"modal-header"},[e.createElementVNode("text",{class:"modal-title"},"添加新计划"),e.createElementVNode("view",{class:"close-button",onClick:i[2]||(i[2]=e=>n.value=!1)},[e.createElementVNode("text",{class:"close-icon"},"×")])]),e.createElementVNode("view",{class:"modal-body"},[e.createElementVNode("view",{class:"form-group"},[e.createElementVNode("text",{class:"form-label"},"计划名称"),e.withDirectives(e.createElementVNode("input",{type:"text","onUpdate:modelValue":i[3]||(i[3]=e=>l.title=e),class:"modern-input",placeholder:"请输入计划名称"},null,512),[[e.vModelText,l.title]])]),e.createElementVNode("view",{class:"form-group"},[e.createElementVNode("text",{class:"form-label"},"选择日期"),e.createElementVNode("picker",{mode:"date",value:l.date,onChange:c},[e.createElementVNode("view",{class:"modern-input picker-input"},[e.createElementVNode("text",{class:"picker-text"},e.toDisplayString(l.date||"选择日期"),1),e.createElementVNode("text",{class:"picker-icon"},"📅")])],40,["value"])]),e.createElementVNode("view",{class:"form-group"},[e.createElementVNode("text",{class:"form-label"},"选择时间"),e.createElementVNode("picker",{mode:"time",value:l.time,onChange:r},[e.createElementVNode("view",{class:"modern-input picker-input"},[e.createElementVNode("text",{class:"picker-text"},e.toDisplayString(l.time||"选择时间"),1),e.createElementVNode("text",{class:"picker-icon"},"⏰")])],40,["value"])])]),e.createElementVNode("view",{class:"modal-footer"},[e.createElementVNode("button",{class:"modern-button cancel-btn",onClick:i[4]||(i[4]=e=>n.value=!1)}," 取消 "),e.createElementVNode("button",{class:"modern-button primary submit-btn",onClick:s}," 添加计划 ")])])])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"bottom-decoration"})]))}},u={__name:"timereminder",setup(a){const t=e.ref(!1),n=e.ref(!1),l=e.ref(-1),o=e.ref([]),c=e.reactive({title:"",date:"",time:"",repeat:""}),r=e=>{const[a,t]=e.time.split(" "),[n,l,o]=a.split("-").map(Number),[c,r]=t.split(":").map(Number),s=new Date(n,l-1,o,c,r).getTime(),d=Date.now();if(s>d){setTimeout((()=>{uni.showToast({title:`提醒: ${e.title}`,icon:"none",duration:5e3})}),s-d)}},s=e=>{c.date=e.detail.value},i=e=>{c.time=e.detail.value},m=()=>{if(!c.title||!c.date||!c.time)return void uni.showToast({title:"请填写完整信息",icon:"none"});const e=`${c.date} ${c.time}`;n.value?o.value[l.value]={title:c.title,time:e}:o.value.push({title:c.title,time:e}),r({title:c.title,time:e}),saveReminders(),u()},u=()=>{t.value=!1,n.value=!1,l.value=-1,c.title="",c.date="",c.time=""},N=()=>{(async()=>{try{const e=await d({url:"/api/reminders",method:"GET"});o.value=e||[]}catch(e){o.value=[]}})(),o.value.forEach((e=>{r(e)}))};return e.onMounted((()=>{N()})),(a,r)=>(e.openBlock(),e.createElementBlock("view",{class:"modern-page"},[e.createElementVNode("view",{class:"modern-header"},[e.createElementVNode("text",{class:"header-title"},"时间提醒"),e.createElementVNode("text",{class:"header-subtitle"},"智能提醒，不错过任何重要时刻"),e.createElementVNode("view",{class:"header-decoration"})]),e.createElementVNode("view",{class:"modern-content"},[e.createElementVNode("view",{class:"modern-card add-reminder-card fade-in-up",onClick:r[0]||(r[0]=e=>t.value=!0)},[e.createElementVNode("view",{class:"add-icon"},[e.createElementVNode("text",{class:"icon-text"},"⏰")]),e.createElementVNode("text",{class:"add-title"},"添加新提醒"),e.createElementVNode("text",{class:"add-desc"},"设置重要事项提醒")]),o.value.length>0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"reminders-section"},[e.createElementVNode("view",{class:"section-header"},[e.createElementVNode("text",{class:"section-title"},"我的提醒"),e.createElementVNode("text",{class:"section-subtitle"},e.toDisplayString(o.value.length)+"个提醒",1)]),e.createElementVNode("view",{class:"reminder-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(o.value,((a,r)=>(e.openBlock(),e.createElementBlock("view",{class:"modern-card reminder-item fade-in-up",key:r},[e.createElementVNode("view",{class:"reminder-header"},[e.createElementVNode("view",{class:"reminder-icon"},[e.createElementVNode("text",{class:"icon-text"},"🔔")]),e.createElementVNode("view",{class:"reminder-info"},[e.createElementVNode("text",{class:"reminder-title"},e.toDisplayString(a.title),1),e.createElementVNode("text",{class:"reminder-time"},e.toDisplayString(a.time),1)])]),e.createElementVNode("view",{class:"reminder-actions"},[e.createElementVNode("button",{class:"modern-button warning action-btn",onClick:e=>(e=>{n.value=!0,l.value=e;const[a,r]=o.value[e].time.split(" ");c.title=o.value[e].title,c.date=a,c.time=r,t.value=!0})(r)}," 编辑 ",8,["onClick"]),e.createElementVNode("button",{class:"modern-button danger action-btn",onClick:e=>(e=>{o.value.splice(e,1),saveReminders()})(r)}," 删除 ",8,["onClick"])])])))),128))])])):e.createCommentVNode("",!0),0===o.value.length?(e.openBlock(),e.createElementBlock("view",{key:1,class:"no-reminders"},[e.createElementVNode("view",{class:"empty-icon"},[e.createElementVNode("text",{class:"icon-text"},"⏰")]),e.createElementVNode("text",{class:"empty-text"},"暂无提醒"),e.createElementVNode("text",{class:"empty-desc"},"点击上方按钮创建您的第一个提醒")])):e.createCommentVNode("",!0)]),t.value?(e.openBlock(),e.createElementBlock("view",{key:0,class:"modern-modal"},[e.createElementVNode("view",{class:"modal-overlay",onClick:u}),e.createElementVNode("view",{class:"modal-content"},[e.createElementVNode("view",{class:"modal-header"},[e.createElementVNode("text",{class:"modal-title"},e.toDisplayString(n.value?"编辑提醒":"添加提醒"),1),e.createElementVNode("view",{class:"close-button",onClick:u},[e.createElementVNode("text",{class:"close-icon"},"×")])]),e.createElementVNode("view",{class:"modal-body"},[e.createElementVNode("view",{class:"form-group"},[e.createElementVNode("text",{class:"form-label"},"提醒内容"),e.withDirectives(e.createElementVNode("input",{type:"text","onUpdate:modelValue":r[1]||(r[1]=e=>c.title=e),class:"modern-input",placeholder:"请输入提醒内容"},null,512),[[e.vModelText,c.title]])]),e.createElementVNode("view",{class:"form-group"},[e.createElementVNode("text",{class:"form-label"},"选择日期"),e.createElementVNode("picker",{mode:"date",value:c.date,onChange:s},[e.createElementVNode("view",{class:"modern-input picker-input"},[e.createElementVNode("text",{class:"picker-text"},e.toDisplayString(c.date||"选择日期"),1),e.createElementVNode("text",{class:"picker-icon"},"📅")])],40,["value"])]),e.createElementVNode("view",{class:"form-group"},[e.createElementVNode("text",{class:"form-label"},"选择时间"),e.createElementVNode("picker",{mode:"time",value:c.time,onChange:i},[e.createElementVNode("view",{class:"modern-input picker-input"},[e.createElementVNode("text",{class:"picker-text"},e.toDisplayString(c.time||"选择时间"),1),e.createElementVNode("text",{class:"picker-icon"},"⏰")])],40,["value"])])]),e.createElementVNode("view",{class:"modal-footer"},[e.createElementVNode("button",{class:"modern-button cancel-btn",onClick:u}," 取消 "),e.createElementVNode("button",{class:"modern-button primary submit-btn",onClick:m},e.toDisplayString(n.value?"保存":"添加"),1)])])])):e.createCommentVNode("",!0),e.createElementVNode("view",{class:"bottom-decoration"})]))}},N={__name:"dailypunch",setup(a){const t=e.computed((()=>{const e=new Date;return`${e.getFullYear()}-${String(e.getMonth()+1).padStart(2,"0")}-${String(e.getDate()).padStart(2,"0")}`})),n=e.ref(!1),l=e.ref([]),o=async()=>{try{const e=await d({url:"/api/punches",method:"GET"});l.value=(e||[]).map((e=>({date:e.date,punched:!0}))),n.value=l.value.some((e=>e.date===t.value&&e.punched))}catch(e){l.value=[],n.value=!1}},r=async()=>{try{await d({url:"/api/punches",method:"POST",data:{date:t.value,content:"",type:"每日打卡",created_at:t.value}}),uni.showToast({title:"打卡成功",icon:"success"}),await o()}catch(e){}};return e.onMounted((async()=>{await(async()=>{try{const e=uni.getStorageSync("punchRecords");if(e){const a=JSON.parse(e);for(const e of a)e.date&&e.punched&&await d({url:"/api/punches",method:"POST",data:{date:e.date,content:"",type:"每日打卡",created_at:e.date}});uni.removeStorageSync("punchRecords")}}catch(e){c("error","at pages/dailypunch/dailypunch.vue:106","本地打卡数据迁移失败",e)}})(),await o()})),(a,o)=>(e.openBlock(),e.createElementBlock("view",{class:"modern-page"},[e.createElementVNode("view",{class:"modern-header"},[e.createElementVNode("text",{class:"header-title"},"每日打卡"),e.createElementVNode("text",{class:"header-subtitle"},"坚持每一天，养成好习惯"),e.createElementVNode("view",{class:"header-decoration"})]),e.createElementVNode("view",{class:"modern-content"},[e.createElementVNode("view",{class:"modern-card date-card fade-in-up"},[e.createElementVNode("view",{class:"date-icon"},[e.createElementVNode("text",{class:"icon-text"},"📅")]),e.createElementVNode("text",{class:"current-date"},e.toDisplayString(t.value),1),e.createElementVNode("text",{class:"date-label"},"今日日期")]),e.createElementVNode("view",{class:"modern-card punch-status-card fade-in-up"},[e.createElementVNode("view",{class:e.normalizeClass(["status-icon",{punched:n.value}])},[e.createElementVNode("text",{class:"icon-text"},e.toDisplayString(n.value?"✅":"⏰"),1)],2),e.createElementVNode("text",{class:e.normalizeClass(["status-text",{punched:n.value}])},e.toDisplayString(n.value?"今日已打卡":"今日未打卡"),3),e.createElementVNode("text",{class:"status-desc"},e.toDisplayString(n.value?"恭喜您完成今日打卡！":"点击下方按钮完成打卡"),1),e.createElementVNode("button",{class:e.normalizeClass(["modern-button punch-button",{success:!n.value,disabled:n.value}]),disabled:n.value,onClick:r},e.toDisplayString(n.value?"已完成":"立即打卡"),11,["disabled"])]),e.createElementVNode("view",{class:"modern-card history-card fade-in-up"},[e.createElementVNode("view",{class:"section-header"},[e.createElementVNode("text",{class:"section-title"},"打卡历史"),e.createElementVNode("text",{class:"section-subtitle"},e.toDisplayString(l.value.length)+"条记录",1)]),e.createElementVNode("view",{class:"history-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(l.value,((a,t)=>(e.openBlock(),e.createElementBlock("view",{class:"history-item",key:t},[e.createElementVNode("view",{class:"history-date-section"},[e.createElementVNode("text",{class:"history-date"},e.toDisplayString(a.date),1)]),e.createElementVNode("view",{class:"history-status-section"},[e.createElementVNode("text",{class:e.normalizeClass(["history-status",{punched:a.punched}])},e.toDisplayString(a.punched?"✅ 已打卡":"❌ 未打卡"),3)])])))),128)),0===l.value.length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"no-records"},[e.createElementVNode("view",{class:"empty-icon"},[e.createElementVNode("text",{class:"icon-text"},"📝")]),e.createElementVNode("text",{class:"empty-text"},"暂无打卡记录"),e.createElementVNode("text",{class:"empty-desc"},"开始您的第一次打卡吧！")])):e.createCommentVNode("",!0)])])]),e.createElementVNode("view",{class:"bottom-decoration"})]))}},v={__name:"schedule",setup(a){const t=e.ref([]),n=e.computed((()=>t.value.length)),l=e.computed((()=>t.value.filter((e=>100===e.progress)).length)),o=e.computed((()=>{if(0===n.value)return 0;const e=t.value.reduce(((e,a)=>e+a.progress),0);return Math.round(e/n.value)}));return e.onMounted((()=>{(async()=>{try{const e=(await d({url:"/api/plans",method:"GET"})||[]).map((e=>({title:e.title,progress:e.completed?100:0})));let a=[];try{a=(await d({url:"/api/reminders",method:"GET"})||[]).map((e=>{const a=new Date(e.time)<new Date?0:50;return{title:e.title,progress:a}}))}catch{}t.value=[...e,...a]}catch(e){t.value=[]}})()})),(a,c)=>(e.openBlock(),e.createElementBlock("view",{class:"modern-page"},[e.createElementVNode("view",{class:"modern-header"},[e.createElementVNode("text",{class:"header-title"},"进度追踪"),e.createElementVNode("text",{class:"header-subtitle"},"可视化学习进度，掌握学习节奏"),e.createElementVNode("view",{class:"header-decoration"})]),e.createElementVNode("view",{class:"modern-content"},[e.createElementVNode("view",{class:"modern-card overview-card fade-in-up"},[e.createElementVNode("view",{class:"card-header"},[e.createElementVNode("view",{class:"header-icon"},[e.createElementVNode("text",{class:"icon-text"},"📊")]),e.createElementVNode("text",{class:"card-title"},"学习概览")]),e.createElementVNode("view",{class:"overview-grid"},[e.createElementVNode("view",{class:"overview-item"},[e.createElementVNode("text",{class:"overview-value"},e.toDisplayString(n.value),1),e.createElementVNode("text",{class:"overview-label"},"总任务数")]),e.createElementVNode("view",{class:"overview-item"},[e.createElementVNode("text",{class:"overview-value completed"},e.toDisplayString(l.value),1),e.createElementVNode("text",{class:"overview-label"},"已完成")]),e.createElementVNode("view",{class:"overview-item"},[e.createElementVNode("text",{class:"overview-value rate"},e.toDisplayString(o.value)+"%",1),e.createElementVNode("text",{class:"overview-label"},"完成率")])])]),t.value.length>0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"tasks-section"},[e.createElementVNode("view",{class:"section-header"},[e.createElementVNode("text",{class:"section-title"},"任务进度"),e.createElementVNode("text",{class:"section-subtitle"},e.toDisplayString(t.value.length)+"个任务",1)]),e.createElementVNode("view",{class:"task-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(t.value,((a,t)=>(e.openBlock(),e.createElementBlock("view",{class:"modern-card task-item fade-in-up",key:t},[e.createElementVNode("view",{class:"task-header"},[e.createElementVNode("view",{class:e.normalizeClass(["task-icon",{completed:100===a.progress}])},[e.createElementVNode("text",{class:"icon-text"},e.toDisplayString(100===a.progress?"✅":"📝"),1)],2),e.createElementVNode("view",{class:"task-info"},[e.createElementVNode("text",{class:"task-title"},e.toDisplayString(a.title),1),e.createElementVNode("text",{class:"task-progress"},e.toDisplayString(a.progress)+"% 完成",1)])]),e.createElementVNode("view",{class:"progress-container"},[e.createElementVNode("view",{class:"progress-bar"},[e.createElementVNode("view",{class:"progress-fill",style:e.normalizeStyle({width:a.progress+"%"})},null,4)])])])))),128))])])):e.createCommentVNode("",!0),0===t.value.length?(e.openBlock(),e.createElementBlock("view",{key:1,class:"no-tasks"},[e.createElementVNode("view",{class:"empty-icon"},[e.createElementVNode("text",{class:"icon-text"},"📊")]),e.createElementVNode("text",{class:"empty-text"},"暂无任务"),e.createElementVNode("text",{class:"empty-desc"},"完成计划和提醒后，这里将显示进度信息")])):e.createCommentVNode("",!0)]),e.createElementVNode("view",{class:"bottom-decoration"})]))}},E={__name:"dataanalysis",setup(a){const t=e.ref(0),n=e.ref(0),l=e.ref(0),o=e.ref(0),c=e.ref((new Date).getFullYear()),r=e.ref((new Date).getMonth()),s=e.ref([]),i=e=>{if(!e.length)return 0;const a=e.sort(((e,a)=>new Date(a.date)-new Date(e.date)));let t=0;const n=new Date,l=new Date(n);l.setDate(n.getDate()-1);let o=a.some((e=>e.date===m(n)&&e.punched))?n:a.some((e=>e.date===m(l)&&e.punched))?l:null;if(!o)return 0;for(const c of a){const e=new Date(c.date);if(c.punched&&m(e)===m(o))t++,o.setDate(o.getDate()-1);else if(e<o)break}return t},m=e=>`${e.getFullYear()}-${String(e.getMonth()+1).padStart(2,"0")}-${String(e.getDate()).padStart(2,"0")}`,u=e.computed((()=>{const e=c.value,a=r.value,t=new Date(e,a,1),n=new Date(e,a+1,0).getDate(),l=t.getDay(),o=[];for(let c=0;c<l;c++)o.push({date:null,punched:!1});for(let c=1;c<=n;c++){const t=`${e}-${String(a+1).padStart(2,"0")}-${String(c).padStart(2,"0")}`,n=s.value.some((e=>e.date===t&&e.punched));o.push({date:c,punched:n})}return o})),N=e.computed((()=>0===t.value?0:Math.round(n.value/t.value*100))),v=e.computed((()=>0===o.value?0:Math.round(l.value/o.value*100))),E=()=>{0===r.value?(r.value=11,c.value--):r.value--},g=()=>{11===r.value?(r.value=0,c.value++):r.value++};return e.onMounted((()=>{(async()=>{try{const e=await d({url:"/api/plans",method:"GET"});l.value=(e||[]).filter((e=>e.completed)).length,o.value=(e||[]).length;const a=await d({url:"/api/punches",method:"GET"});s.value=a||[],t.value=s.value.filter((e=>e.punched)).length,n.value=i(s.value)}catch(e){t.value=0,n.value=0,l.value=0,o.value=0,s.value=[]}})()})),(a,o)=>(e.openBlock(),e.createElementBlock("view",{class:"modern-page"},[e.createElementVNode("view",{class:"modern-header"},[e.createElementVNode("text",{class:"header-title"},"数据分析"),e.createElementVNode("text",{class:"header-subtitle"},"深度分析学习数据，优化学习策略"),e.createElementVNode("view",{class:"header-decoration"})]),e.createElementVNode("view",{class:"modern-content"},[e.createElementVNode("view",{class:"modern-card stats-card fade-in-up"},[e.createElementVNode("view",{class:"card-header"},[e.createElementVNode("view",{class:"header-icon"},[e.createElementVNode("text",{class:"icon-text"},"📈")]),e.createElementVNode("text",{class:"card-title"},"学习统计")]),e.createElementVNode("view",{class:"stats-grid"},[e.createElementVNode("view",{class:"stat-item"},[e.createElementVNode("text",{class:"stat-value"},e.toDisplayString(t.value),1),e.createElementVNode("text",{class:"stat-label"},"总打卡天数")]),e.createElementVNode("view",{class:"stat-item"},[e.createElementVNode("text",{class:"stat-value streak"},e.toDisplayString(n.value),1),e.createElementVNode("text",{class:"stat-label"},"连续打卡天数")]),e.createElementVNode("view",{class:"stat-item"},[e.createElementVNode("text",{class:"stat-value completed"},e.toDisplayString(l.value),1),e.createElementVNode("text",{class:"stat-label"},"完成计划数")])])]),e.createElementVNode("view",{class:"modern-card calendar-card fade-in-up"},[e.createElementVNode("view",{class:"calendar-header"},[e.createElementVNode("view",{class:"calendar-nav"},[e.createElementVNode("button",{class:"modern-button nav-button",onClick:E},[e.createElementVNode("text",{class:"nav-icon"},"◄")]),e.createElementVNode("text",{class:"calendar-title"},e.toDisplayString(c.value)+"年"+e.toDisplayString(r.value+1)+"月",1),e.createElementVNode("button",{class:"modern-button nav-button",onClick:g},[e.createElementVNode("text",{class:"nav-icon"},"►")])])]),e.createElementVNode("view",{class:"calendar-grid"},[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(["日","一","二","三","四","五","六"],(a=>e.createElementVNode("text",{class:"day-name",key:a},e.toDisplayString(a),1))),64)),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(u.value,((a,t)=>(e.openBlock(),e.createElementBlock("view",{key:t,class:e.normalizeClass(["day-cell",{punched:a.punched,empty:!a.date}])},[e.createElementVNode("text",{class:"day-text"},e.toDisplayString(a.date||""),1)],2)))),128))])]),e.createElementVNode("view",{class:"modern-card rates-card fade-in-up"},[e.createElementVNode("view",{class:"card-header"},[e.createElementVNode("view",{class:"header-icon"},[e.createElementVNode("text",{class:"icon-text"},"📊")]),e.createElementVNode("text",{class:"card-title"},"完成率分析")]),e.createElementVNode("view",{class:"rates-grid"},[e.createElementVNode("view",{class:"rate-item"},[e.createElementVNode("view",{class:"rate-circle"},[e.createElementVNode("text",{class:"rate-value"},e.toDisplayString(N.value)+"%",1)]),e.createElementVNode("text",{class:"rate-label"},"连续打卡率")]),e.createElementVNode("view",{class:"rate-item"},[e.createElementVNode("view",{class:"rate-circle"},[e.createElementVNode("text",{class:"rate-value"},e.toDisplayString(v.value)+"%",1)]),e.createElementVNode("text",{class:"rate-label"},"计划完成率")])])])]),e.createElementVNode("view",{class:"bottom-decoration"})]))}},g={__name:"evaluate",setup(a){const t=e.ref(0),n=e.ref(""),l=e.ref([]),o=e.ref(""),r=e=>{const a=[];let t=0;e.forEach((e=>{const t=e.detail.match(/平均专注度 (\d+)%/);t&&a.push(parseInt(t[1]))})),t=e.reduce(((e,a)=>{const t=a.detail.match(/监测时长: (\d{2}):(\d{2}):(\d{2})/);if(t){return e+60*parseInt(t[1])+parseInt(t[2])+parseInt(t[3])/60}return e}),0);return{avgFocus:a.length>0?Math.round(a.reduce(((e,a)=>e+a),0)/a.length):0,totalStudyTime:Math.round(t)}},s=e=>{const a=e.filter((e=>e.punched)).length,t=new Date("2025-01-01"),n=new Date,l=Math.ceil((n-t)/864e5);return{completionRate:l>0?Math.round(a/l*100):0}},i=e=>e>=70?"专注时间较长，效率高。":e>=50?"专注度一般，需提升稳定性。":"专注度较低，建议优化学习环境。",m=e=>e>=70?"大部分任务按时完成。":e>=50?"完成率中等，需加强坚持。":"完成率较低，建议调整计划。",u=e=>e>=600?"学习投入非常充分。":e>=300?"学习投入稳定。":"学习时长较短，建议增加投入。",N=e=>e>=70?"您的学习表现优秀，继续保持！":e>=50?"学习表现良好，稍作调整更佳。":"学习表现有待提升，建议优化学习策略。",v=e=>{switch(e){case"专注度":return"🎯";case"完成率":return"✅";case"学习时长":return"⏰";default:return"📊"}},E=async()=>{if(o.value.trim())try{await d({url:"/api/feedback",method:"POST",data:{content:o.value}}),uni.showToast({title:"反馈提交成功",icon:"success"}),o.value=""}catch(e){uni.showToast({title:(null==e?void 0:e.error)||"提交失败",icon:"none"})}else uni.showToast({title:"反馈内容不能为空",icon:"none"})};return e.onMounted((()=>{(()=>{try{const e=uni.getStorageSync("monitorRecords")||"[]",a=JSON.parse(e),o=r(a),c=uni.getStorageSync("punchRecords")||"[]",d=JSON.parse(c),v=s(d);l.value=[{label:"专注度",score:o.avgFocus,comment:i(o.avgFocus)},{label:"完成率",score:v.completionRate,comment:m(v.completionRate)},{label:"学习时长",score:o.totalStudyTime,comment:u(o.totalStudyTime)}],t.value=Math.round((o.avgFocus+v.completionRate)/2),n.value=N(t.value);const E=uni.getStorageSync("feedbacks")||"[]";feedbacks.value=JSON.parse(E)}catch(e){c("error","at pages/evaluate/evaluate.vue:123","加载数据失败",e),l.value=[{label:"专注度",score:0,comment:"暂无数据"},{label:"完成率",score:0,comment:"暂无数据"},{label:"学习时长",score:0,comment:"暂无数据"}],t.value=0,n.value="暂无评价数据",feedbacks.value=[]}})()})),(a,c)=>(e.openBlock(),e.createElementBlock("view",{class:"modern-page"},[e.createElementVNode("view",{class:"modern-header"},[e.createElementVNode("text",{class:"header-title"},"学习评价"),e.createElementVNode("text",{class:"header-subtitle"},"全面评估学习表现，持续改进提升"),e.createElementVNode("view",{class:"header-decoration"})]),e.createElementVNode("view",{class:"modern-content"},[e.createElementVNode("view",{class:"modern-card overall-card fade-in-up"},[e.createElementVNode("view",{class:"card-header"},[e.createElementVNode("view",{class:"header-icon"},[e.createElementVNode("text",{class:"icon-text"},"⭐")]),e.createElementVNode("text",{class:"card-title"},"总体评价")]),e.createElementVNode("view",{class:"overall-content"},[e.createElementVNode("view",{class:"score-circle"},[e.createElementVNode("text",{class:"score-value"},e.toDisplayString(t.value),1),e.createElementVNode("text",{class:"score-unit"},"分")]),e.createElementVNode("text",{class:"evaluation-text"},e.toDisplayString(n.value),1)])]),e.createElementVNode("view",{class:"modern-card detailed-card fade-in-up"},[e.createElementVNode("view",{class:"card-header"},[e.createElementVNode("view",{class:"header-icon"},[e.createElementVNode("text",{class:"icon-text"},"📊")]),e.createElementVNode("text",{class:"card-title"},"详细评价")]),e.createElementVNode("view",{class:"evaluation-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(l.value,((a,t)=>(e.openBlock(),e.createElementBlock("view",{class:"evaluation-item",key:t},[e.createElementVNode("view",{class:"item-header"},[e.createElementVNode("view",{class:e.normalizeClass(["item-icon",`item-${t}`])},[e.createElementVNode("text",{class:"icon-text"},e.toDisplayString(v(a.label)),1)],2),e.createElementVNode("view",{class:"item-info"},[e.createElementVNode("text",{class:"item-label"},e.toDisplayString(a.label),1),e.createElementVNode("text",{class:"item-score"},e.toDisplayString("学习时长"===a.label?`${a.score}分钟`:`${a.score}%`),1)])]),e.createElementVNode("text",{class:"item-comment"},e.toDisplayString(a.comment),1)])))),128))])]),e.createElementVNode("view",{class:"modern-card feedback-card fade-in-up"},[e.createElementVNode("view",{class:"card-header"},[e.createElementVNode("view",{class:"header-icon"},[e.createElementVNode("text",{class:"icon-text"},"💬")]),e.createElementVNode("text",{class:"card-title"},"提交反馈")]),e.createElementVNode("view",{class:"feedback-content"},[e.withDirectives(e.createElementVNode("textarea",{class:"modern-textarea","onUpdate:modelValue":c[0]||(c[0]=e=>o.value=e),placeholder:"请输入您的反馈意见..."},null,512),[[e.vModelText,o.value]]),e.createElementVNode("button",{class:"modern-button primary submit-button",onClick:E}," 提交反馈 ")])])]),e.createElementVNode("view",{class:"bottom-decoration"})]))}},V={__name:"achievementwall",setup(a){const t=e.ref([]),n=e.ref([]),l=e.ref([]),o=()=>{const e={};t.value.forEach((a=>{e[a.title]=a.unlocked})),n.value.forEach((a=>{e[a.title]=a.unlocked})),l.value.forEach((a=>{e[a.title]=a.unlocked})),uni.setStorageSync("achievements",JSON.stringify(e))},r=e=>{let a=0;return e.forEach((e=>{const t=e.detail.match(/监测时长: (\d{2}):(\d{2}):(\d{2})/);if(t){const e=parseInt(t[1]),n=parseInt(t[2]),l=parseInt(t[3]);a+=60*e+n+l/60}})),{totalStudyTime:Math.round(a)}},s=e=>{const a=e.filter((e=>e.punched)).length,t=e.sort(((e,a)=>new Date(a.date)-new Date(e.date)));let n=0;const l=new Date,o=new Date(l);o.setDate(l.getDate()-1);let c=t.some((e=>e.date===d(l)&&e.punched))?l:t.some((e=>e.date===d(o)&&e.punched))?o:null;if(c)for(const r of t){const e=new Date(r.date);if(r.punched&&d(e)===d(c))n++,c.setDate(c.getDate()-1);else if(e<c)break}return{totalPunchDays:a,streakDays:n}},d=e=>`${e.getFullYear()}-${String(e.getMonth()+1).padStart(2,"0")}-${String(e.getDate()).padStart(2,"0")}`,i=e.computed((()=>t.value.filter((e=>e.unlocked)).length+n.value.filter((e=>e.unlocked)).length+l.value.filter((e=>e.unlocked)).length)),m=e.computed((()=>t.value.length+n.value.length+l.value.length));return e.onMounted((()=>{(()=>{try{const e=uni.getStorageSync("achievements")||"{}",a=JSON.parse(e),c=uni.getStorageSync("monitorRecords")||"[]",d=JSON.parse(c),i=uni.getStorageSync("punchRecords")||"[]",m=JSON.parse(i),u=r(d),N=s(m),v={cumulativePunch:[{title:"打卡初体验",icon:"🎉",description:"累计打卡1天",condition:N.totalPunchDays>=1},{title:"打卡入门",icon:"🏅",description:"累计打卡5天",condition:N.totalPunchDays>=5},{title:"打卡小成",icon:"🌟",description:"累计打卡10天",condition:N.totalPunchDays>=10},{title:"打卡达人",icon:"✅",description:"累计打卡30天",condition:N.totalPunchDays>=30},{title:"打卡大师",icon:"👑",description:"累计打卡100天",condition:N.totalPunchDays>=100}],streakPunch:[{title:"坚持第一步",icon:"🚀",description:"连续打卡3天",condition:N.streakDays>=3},{title:"习惯养成",icon:"🌱",description:"连续打卡7天",condition:N.streakDays>=7},{title:"持之以恒",icon:"💪",description:"连续打卡14天",condition:N.streakDays>=14},{title:"不懈努力",icon:"🔥",description:"连续打卡30天",condition:N.streakDays>=30},{title:"毅力王者",icon:"🏆",description:"连续打卡60天",condition:N.streakDays>=60}],focus:[{title:"专注新手",icon:"🕒",description:"累计专注30分钟",condition:u.totalStudyTime>=30},{title:"专注能手",icon:"🎯",description:"累计专注2小时",condition:u.totalStudyTime>=120},{title:"专注大师",icon:"✨",description:"累计专注5小时",condition:u.totalStudyTime>=300},{title:"专注王者",icon:"🌟",description:"累计专注10小时",condition:u.totalStudyTime>=600},{title:"专注大王",icon:"🛸",description:"累计专注15小时",condition:u.totalStudyTime>=900}]};t.value=v.cumulativePunch.map((e=>({...e,unlocked:a[e.title]||e.condition}))),n.value=v.streakPunch.map((e=>({...e,unlocked:a[e.title]||e.condition}))),l.value=v.focus.map((e=>({...e,unlocked:a[e.title]||e.condition}))),o()}catch(e){c("error","at pages/achievementwall/achievementwall.vue:200","加载成就数据失败",e),t.value=[],n.value=[],l.value=[]}})()})),(a,o)=>(e.openBlock(),e.createElementBlock("view",{class:"modern-page"},[e.createElementVNode("view",{class:"modern-header"},[e.createElementVNode("text",{class:"header-title"},"成就墙"),e.createElementVNode("text",{class:"header-subtitle"},"展示学习成就，激励持续进步"),e.createElementVNode("view",{class:"header-decoration"})]),e.createElementVNode("view",{class:"modern-content"},[e.createElementVNode("view",{class:"modern-card stats-card fade-in-up"},[e.createElementVNode("view",{class:"card-header"},[e.createElementVNode("view",{class:"header-icon"},[e.createElementVNode("text",{class:"icon-text"},"🏆")]),e.createElementVNode("text",{class:"card-title"},"成就统计")]),e.createElementVNode("view",{class:"stats-grid"},[e.createElementVNode("view",{class:"stat-item"},[e.createElementVNode("text",{class:"stat-value unlocked"},e.toDisplayString(i.value),1),e.createElementVNode("text",{class:"stat-label"},"已解锁成就")]),e.createElementVNode("view",{class:"stat-item"},[e.createElementVNode("text",{class:"stat-value total"},e.toDisplayString(m.value),1),e.createElementVNode("text",{class:"stat-label"},"总成就数")])]),e.createElementVNode("view",{class:"progress-section"},[e.createElementVNode("view",{class:"progress-bar"},[e.createElementVNode("view",{class:"progress-fill",style:e.normalizeStyle({width:i.value/m.value*100+"%"})},null,4)]),e.createElementVNode("text",{class:"progress-text"},"完成度 "+e.toDisplayString(Math.round(i.value/m.value*100))+"%",1)])]),e.createElementVNode("view",{class:"modern-card category-card fade-in-up"},[e.createElementVNode("view",{class:"category-header"},[e.createElementVNode("view",{class:"category-icon"},[e.createElementVNode("text",{class:"icon-text"},"📅")]),e.createElementVNode("text",{class:"category-title"},"累计打卡成就")]),e.createElementVNode("scroll-view",{class:"achievement-scroll","scroll-x":"","enable-flex":""},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(t.value,((a,t)=>(e.openBlock(),e.createElementBlock("view",{class:"achievement-item",key:t},[e.createElementVNode("view",{class:e.normalizeClass(["achievement-badge",{locked:!a.unlocked}])},[e.createElementVNode("text",{class:"badge-icon"},e.toDisplayString(a.icon),1)],2),e.createElementVNode("text",{class:"achievement-title"},e.toDisplayString(a.title),1),e.createElementVNode("text",{class:"achievement-desc"},e.toDisplayString(a.description),1),e.createElementVNode("view",{class:e.normalizeClass(["achievement-status",{unlocked:a.unlocked}])},[e.createElementVNode("text",{class:"status-text"},e.toDisplayString(a.unlocked?"已解锁":"未解锁"),1)],2)])))),128)),0===t.value.length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"no-achievements"},[e.createElementVNode("view",{class:"empty-icon"},[e.createElementVNode("text",{class:"icon-text"},"🎯")]),e.createElementVNode("text",{class:"empty-text"},"暂无成就")])):e.createCommentVNode("",!0)])]),e.createElementVNode("view",{class:"modern-card category-card fade-in-up"},[e.createElementVNode("view",{class:"category-header"},[e.createElementVNode("view",{class:"category-icon"},[e.createElementVNode("text",{class:"icon-text"},"🔥")]),e.createElementVNode("text",{class:"category-title"},"连续打卡成就")]),e.createElementVNode("scroll-view",{class:"achievement-scroll","scroll-x":"","enable-flex":""},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(n.value,((a,t)=>(e.openBlock(),e.createElementBlock("view",{class:"achievement-item",key:t},[e.createElementVNode("view",{class:e.normalizeClass(["achievement-badge",{locked:!a.unlocked}])},[e.createElementVNode("text",{class:"badge-icon"},e.toDisplayString(a.icon),1)],2),e.createElementVNode("text",{class:"achievement-title"},e.toDisplayString(a.title),1),e.createElementVNode("text",{class:"achievement-desc"},e.toDisplayString(a.description),1),e.createElementVNode("view",{class:e.normalizeClass(["achievement-status",{unlocked:a.unlocked}])},[e.createElementVNode("text",{class:"status-text"},e.toDisplayString(a.unlocked?"已解锁":"未解锁"),1)],2)])))),128)),0===n.value.length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"no-achievements"},[e.createElementVNode("view",{class:"empty-icon"},[e.createElementVNode("text",{class:"icon-text"},"🎯")]),e.createElementVNode("text",{class:"empty-text"},"暂无成就")])):e.createCommentVNode("",!0)])]),e.createElementVNode("view",{class:"modern-card category-card fade-in-up"},[e.createElementVNode("view",{class:"category-header"},[e.createElementVNode("view",{class:"category-icon"},[e.createElementVNode("text",{class:"icon-text"},"🎯")]),e.createElementVNode("text",{class:"category-title"},"专注成就")]),e.createElementVNode("scroll-view",{class:"achievement-scroll","scroll-x":"","enable-flex":""},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(l.value,((a,t)=>(e.openBlock(),e.createElementBlock("view",{class:"achievement-item",key:t},[e.createElementVNode("view",{class:e.normalizeClass(["achievement-badge",{locked:!a.unlocked}])},[e.createElementVNode("text",{class:"badge-icon"},e.toDisplayString(a.icon),1)],2),e.createElementVNode("text",{class:"achievement-title"},e.toDisplayString(a.title),1),e.createElementVNode("text",{class:"achievement-desc"},e.toDisplayString(a.description),1),e.createElementVNode("view",{class:e.normalizeClass(["achievement-status",{unlocked:a.unlocked}])},[e.createElementVNode("text",{class:"status-text"},e.toDisplayString(a.unlocked?"已解锁":"未解锁"),1)],2)])))),128)),0===l.value.length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"no-achievements"},[e.createElementVNode("view",{class:"empty-icon"},[e.createElementVNode("text",{class:"icon-text"},"🎯")]),e.createElementVNode("text",{class:"empty-text"},"暂无成就")])):e.createCommentVNode("",!0)])])]),e.createElementVNode("view",{class:"bottom-decoration"})]))}},p={__name:"saying",setup(a){const t=e.ref([{text:"不积跬步，无以至千里",author:"荀子"},{text:"成功的秘诀在于坚持",author:"爱迪生"},{text:"每一次努力，都是在靠近梦想",author:"佚名"},{text:"只要路是对的，就不怕路远",author:"佚名"},{text:"今天的努力，是为了明天的自由",author:"佚名"},{text:"失败是成功之母",author:"谚语"},{text:"行动是成功的阶梯",author:"佚名"},{text:"相信自己，你比想象中更强大",author:"佚名"},{text:"时间是最好的老师",author:"佚名"},{text:"不怕慢，只怕站",author:"谚语"},{text:"努力不一定成功，但放弃一定失败",author:"佚名"},{text:"生活不会辜负每一个努力的人",author:"佚名"},{text:"坚持到底，就是胜利",author:"佚名"},{text:"每一步都在塑造更好的自己",author:"佚名"},{text:"成功属于那些不轻言放弃的人",author:"佚名"},{text:"用汗水浇灌梦想",author:"佚名"},{text:"没有天生的强者，只有不懈的努力",author:"佚名"},{text:"勇敢追梦，未来可期",author:"佚名"},{text:"学习是改变命运的钥匙",author:"佚名"},{text:"每一天都是新的开始",author:"佚名"}]),n=e.ref({text:"",author:""}),l=e.ref(!1),o=e.ref([]),r=()=>{try{uni.setStorageSync("favorites",JSON.stringify(o.value))}catch(e){c("error","at pages/saying/saying.vue:127","保存收藏数据失败",e)}},s=()=>{const e=Math.floor(Math.random()*t.value.length);n.value=t.value[e],l.value=o.value.some((e=>e.text===n.value.text))},d=()=>{s()},i=()=>{if(l.value){const e=o.value.findIndex((e=>e.text===n.value.text));-1!==e&&o.value.splice(e,1)}else o.value.push({...n.value});l.value=!l.value,r()};return e.onMounted((()=>{(()=>{try{const e=uni.getStorageSync("favorites")||"[]";o.value=JSON.parse(e),s()}catch(e){c("error","at pages/saying/saying.vue:116","加载收藏数据失败",e),o.value=[],s()}})()})),(a,t)=>(e.openBlock(),e.createElementBlock("view",{class:"modern-page"},[e.createElementVNode("view",{class:"modern-header"},[e.createElementVNode("text",{class:"header-title"},"激励语录"),e.createElementVNode("text",{class:"header-subtitle"},"每日正能量，点亮学习之路"),e.createElementVNode("view",{class:"header-decoration"})]),e.createElementVNode("view",{class:"modern-content"},[e.createElementVNode("view",{class:"modern-card current-saying-card fade-in-up"},[e.createElementVNode("view",{class:"quote-icon"},[e.createElementVNode("text",{class:"icon-text"},"💡")]),e.createElementVNode("text",{class:"saying-text"},'"'+e.toDisplayString(n.value.text)+'"',1),e.createElementVNode("text",{class:"saying-author"},"—— "+e.toDisplayString(n.value.author),1),e.createElementVNode("view",{class:"action-buttons"},[e.createElementVNode("button",{class:"modern-button primary next-btn",onClick:d},[e.createElementVNode("text",{class:"btn-icon"},"🔄"),e.createElementVNode("text",{class:"btn-text"},"下一条")]),e.createElementVNode("button",{class:e.normalizeClass(["modern-button favorite-btn",{success:!l.value,warning:l.value}]),onClick:i},[e.createElementVNode("text",{class:"btn-icon"},e.toDisplayString(l.value?"💖":"🤍"),1),e.createElementVNode("text",{class:"btn-text"},e.toDisplayString(l.value?"取消收藏":"收藏"),1)],2)])]),e.createElementVNode("view",{class:"modern-card favorites-card fade-in-up"},[e.createElementVNode("view",{class:"card-header"},[e.createElementVNode("view",{class:"header-icon"},[e.createElementVNode("text",{class:"icon-text"},"💖")]),e.createElementVNode("text",{class:"card-title"},"我的收藏"),e.createElementVNode("text",{class:"card-subtitle"},e.toDisplayString(o.value.length)+"条语录",1)]),o.value.length>0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"favorites-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(o.value,((a,t)=>(e.openBlock(),e.createElementBlock("view",{class:"favorite-item",key:t},[e.createElementVNode("view",{class:"quote-content"},[e.createElementVNode("text",{class:"favorite-text"},'"'+e.toDisplayString(a.text)+'"',1),e.createElementVNode("text",{class:"favorite-author"},"—— "+e.toDisplayString(a.author),1)]),e.createElementVNode("button",{class:"modern-button danger remove-btn",onClick:e=>(e=>{o.value.splice(e,1),l.value=o.value.some((e=>e.text===n.value.text)),r()})(t)},[e.createElementVNode("text",{class:"btn-icon"},"🗑️")],8,["onClick"])])))),128))])):e.createCommentVNode("",!0),0===o.value.length?(e.openBlock(),e.createElementBlock("view",{key:1,class:"no-favorites"},[e.createElementVNode("view",{class:"empty-icon"},[e.createElementVNode("text",{class:"icon-text"},"📝")]),e.createElementVNode("text",{class:"empty-text"},"暂无收藏语录"),e.createElementVNode("text",{class:"empty-desc"},"收藏喜欢的语录，随时查看")])):e.createCommentVNode("",!0)])]),e.createElementVNode("view",{class:"bottom-decoration"})]))}},w={__name:"rankinglist",setup(a){const t=e.ref([]),n=e=>0===e?"rank-first":1===e?"rank-second":2===e?"rank-third":"rank-normal",l=e=>0===e?"👑":1===e?"🥈":2===e?"🥉":"",o=()=>{uni.showModal({title:"排行榜说明",content:"排行榜显示你和好友的连续打卡天数。你的打卡数据来自每日打卡记录，排名按连续打卡天数降序排列。",showCancel:!1,confirmText:"知道了"})};return e.onMounted((()=>{(async()=>{try{const e=await d({url:"/api/rankings/streak",method:"GET"});t.value=e||[]}catch(e){t.value=[],uni.showToast({title:"加载排行榜失败",icon:"none"})}})()})),(a,c)=>(e.openBlock(),e.createElementBlock("view",{class:"modern-page"},[e.createElementVNode("view",{class:"modern-header"},[e.createElementVNode("text",{class:"header-title"},"学习排行榜"),e.createElementVNode("text",{class:"header-subtitle"},"与伙伴一起进步，见证彼此成长"),e.createElementVNode("view",{class:"header-decoration",onClick:o},[e.createElementVNode("text",{class:"help-icon"},"?")])]),e.createElementVNode("view",{class:"modern-content"},[e.createElementVNode("view",{class:"modern-card ranking-card fade-in-up"},[e.createElementVNode("view",{class:"card-header"},[e.createElementVNode("view",{class:"header-icon"},[e.createElementVNode("text",{class:"icon-text"},"🏅")]),e.createElementVNode("text",{class:"card-title"},"连续打卡排行榜"),e.createElementVNode("text",{class:"card-subtitle"},e.toDisplayString(t.value.length)+"位学习者",1)]),t.value.length>0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"ranking-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(t.value,((a,t)=>(e.openBlock(),e.createElementBlock("view",{class:"ranking-item",key:t},[e.createElementVNode("view",{class:e.normalizeClass(["rank-badge",n(t)])},[e.createElementVNode("text",{class:"rank-number"},e.toDisplayString(t+1),1),t<3?(e.openBlock(),e.createElementBlock("text",{key:0,class:"rank-icon"},e.toDisplayString(l(t)),1)):e.createCommentVNode("",!0)],2),e.createElementVNode("view",{class:"user-avatar"},[e.createElementVNode("image",{src:a.avatar,mode:"aspectFill",class:"avatar-image"},null,8,["src"])]),e.createElementVNode("view",{class:"user-info"},[e.createElementVNode("view",{class:"user-name-row"},[e.createElementVNode("text",{class:"user-flag"},e.toDisplayString(a.flag),1),e.createElementVNode("text",{class:"user-name"},e.toDisplayString(a.name),1)]),e.createElementVNode("text",{class:"user-score"},"连续打卡 "+e.toDisplayString(a.streakDays)+" 天",1)]),e.createElementVNode("view",{class:"score-display"},[e.createElementVNode("text",{class:"score-number"},e.toDisplayString(a.streakDays),1),e.createElementVNode("text",{class:"score-unit"},"天")])])))),128))])):e.createCommentVNode("",!0),0===t.value.length?(e.openBlock(),e.createElementBlock("view",{key:1,class:"no-ranking"},[e.createElementVNode("view",{class:"empty-icon"},[e.createElementVNode("text",{class:"icon-text"},"🏆")]),e.createElementVNode("text",{class:"empty-text"},"暂无排行数据"),e.createElementVNode("text",{class:"empty-desc"},"开始打卡，加入排行榜吧！")])):e.createCommentVNode("",!0)])]),e.createElementVNode("view",{class:"bottom-decoration"})]))}},h={__name:"friends",setup(a){const t=e.ref([]),n=e.ref(""),l=async()=>{const e=await d({url:"/api/friends",method:"GET"});t.value=e||[]},o=async()=>{if(n.value.trim())try{await d({url:"/api/friends",method:"POST",data:{username:n.value}}),n.value="",uni.showToast({title:"添加成功",icon:"success"}),await l()}catch(e){uni.showToast({title:(null==e?void 0:e.error)||"添加失败",icon:"none"})}else uni.showToast({title:"请输入好友用户名",icon:"none"})};return e.onMounted((()=>{l()})),(a,c)=>(e.openBlock(),e.createElementBlock("view",{class:"modern-page"},[e.createElementVNode("view",{class:"modern-header"},[e.createElementVNode("text",{class:"header-title"},"学习好友"),e.createElementVNode("text",{class:"header-subtitle"},"添加学习伙伴，共同成长进步"),e.createElementVNode("view",{class:"header-decoration"})]),e.createElementVNode("view",{class:"modern-content"},[e.createElementVNode("view",{class:"modern-card add-friend-card fade-in-up"},[e.createElementVNode("view",{class:"card-header"},[e.createElementVNode("view",{class:"header-icon"},[e.createElementVNode("text",{class:"icon-text"},"➕")]),e.createElementVNode("text",{class:"card-title"},"添加好友")]),e.createElementVNode("view",{class:"add-friend-form"},[e.withDirectives(e.createElementVNode("input",{class:"modern-input","onUpdate:modelValue":c[0]||(c[0]=e=>n.value=e),placeholder:"输入好友用户名"},null,512),[[e.vModelText,n.value]]),e.createElementVNode("button",{class:"modern-button primary add-btn",onClick:o},[e.createElementVNode("text",{class:"btn-icon"},"👥"),e.createElementVNode("text",{class:"btn-text"},"添加")])])]),e.createElementVNode("view",{class:"modern-card friends-card fade-in-up"},[e.createElementVNode("view",{class:"card-header"},[e.createElementVNode("view",{class:"header-icon"},[e.createElementVNode("text",{class:"icon-text"},"👥")]),e.createElementVNode("text",{class:"card-title"},"我的好友"),e.createElementVNode("text",{class:"card-subtitle"},e.toDisplayString(t.value.length)+"位好友",1)]),t.value.length>0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"friend-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(t.value,((a,n)=>(e.openBlock(),e.createElementBlock("view",{class:"friend-item",key:n},[e.createElementVNode("view",{class:"friend-avatar"},[e.createElementVNode("image",{src:a.avatar,mode:"aspectFill",class:"avatar-image"},null,8,["src"]),e.createElementVNode("view",{class:"online-status"})]),e.createElementVNode("view",{class:"friend-info"},[e.createElementVNode("view",{class:"friend-name-row"},[e.createElementVNode("text",{class:"friend-flag"},e.toDisplayString(a.flag),1),e.createElementVNode("text",{class:"friend-name"},e.toDisplayString(a.name),1)]),e.createElementVNode("text",{class:"friend-status"},"学习伙伴")]),e.createElementVNode("button",{class:"modern-button danger remove-btn",onClick:e=>(async e=>{const a=t.value[e];try{await d({url:`/api/friends/${a.friend_id}`,method:"DELETE"}),uni.showToast({title:"删除成功",icon:"success"}),await l()}catch(n){uni.showToast({title:(null==n?void 0:n.error)||"删除失败",icon:"none"})}})(n)},[e.createElementVNode("text",{class:"btn-icon"},"🗑️")],8,["onClick"])])))),128))])):e.createCommentVNode("",!0),0===t.value.length?(e.openBlock(),e.createElementBlock("view",{key:1,class:"no-friends"},[e.createElementVNode("view",{class:"empty-icon"},[e.createElementVNode("text",{class:"icon-text"},"👥")]),e.createElementVNode("text",{class:"empty-text"},"暂无好友"),e.createElementVNode("text",{class:"empty-desc"},"添加学习伙伴，一起进步吧！")])):e.createCommentVNode("",!0)])]),e.createElementVNode("view",{class:"bottom-decoration"})]))}},f={__name:"social",setup(a){const t=e.ref([]),n=e.ref(""),l=e.ref({}),o=async()=>{const e=await d({url:"/api/posts",method:"GET"});t.value=(e||[]).map((e=>({...e,name:e.username,flag:"",time:e.created_at,text:e.content,liked:!1,showComment:!1})))},c=async()=>{n.value.trim()&&(await d({url:"/api/posts",method:"POST",data:{content:n.value}}),n.value="",await o())};return e.onMounted((()=>{o()})),(a,r)=>(e.openBlock(),e.createElementBlock("view",{class:"modern-page"},[e.createElementVNode("view",{class:"modern-header"},[e.createElementVNode("text",{class:"header-title"},"学习动态"),e.createElementVNode("text",{class:"header-subtitle"},"分享学习心得，交流学习经验"),e.createElementVNode("view",{class:"header-decoration"})]),e.createElementVNode("view",{class:"modern-content"},[e.createElementVNode("view",{class:"modern-card post-card fade-in-up"},[e.createElementVNode("view",{class:"card-header"},[e.createElementVNode("view",{class:"header-icon"},[e.createElementVNode("text",{class:"icon-text"},"✍️")]),e.createElementVNode("text",{class:"card-title"},"发布动态")]),e.createElementVNode("view",{class:"post-form"},[e.withDirectives(e.createElementVNode("textarea",{class:"modern-textarea","onUpdate:modelValue":r[0]||(r[0]=e=>n.value=e),placeholder:"分享你的学习动态..."},null,512),[[e.vModelText,n.value]]),e.createElementVNode("button",{class:"modern-button primary post-btn",onClick:c},[e.createElementVNode("text",{class:"btn-icon"},"📤"),e.createElementVNode("text",{class:"btn-text"},"发布")])])]),e.createElementVNode("view",{class:"modern-card posts-card fade-in-up"},[e.createElementVNode("view",{class:"card-header"},[e.createElementVNode("view",{class:"header-icon"},[e.createElementVNode("text",{class:"icon-text"},"💬")]),e.createElementVNode("text",{class:"card-title"},"学习动态"),e.createElementVNode("text",{class:"card-subtitle"},e.toDisplayString(t.value.length)+"条动态",1)]),t.value.length>0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"posts-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(t.value,((a,n)=>(e.openBlock(),e.createElementBlock("view",{class:"post-item",key:n},[e.createElementVNode("view",{class:"post-header"},[e.createElementVNode("view",{class:"user-avatar"},[e.createElementVNode("image",{src:a.avatar,mode:"aspectFill",class:"avatar-image"},null,8,["src"])]),e.createElementVNode("view",{class:"user-info"},[e.createElementVNode("view",{class:"user-name-row"},[e.createElementVNode("text",{class:"user-flag"},e.toDisplayString(a.flag),1),e.createElementVNode("text",{class:"user-name"},e.toDisplayString(a.name),1)]),e.createElementVNode("text",{class:"post-time"},e.toDisplayString(a.time),1)]),"我"===a.name?(e.openBlock(),e.createElementBlock("button",{key:0,class:"modern-button danger delete-btn",onClick:e=>(async e=>{const a=t.value[e];await d({url:`/api/posts/${a.id}`,method:"DELETE"}),await o()})(n)},[e.createElementVNode("text",{class:"btn-icon"},"🗑️")],8,["onClick"])):e.createCommentVNode("",!0)]),e.createElementVNode("view",{class:"post-content"},[e.createElementVNode("text",{class:"post-text"},e.toDisplayString(a.text),1)]),e.createElementVNode("view",{class:"post-actions"},[e.createElementVNode("button",{class:e.normalizeClass(["action-btn like-btn",{liked:a.liked}]),onClick:e=>(async e=>{const a=t.value[e];await d({url:`/api/posts/${a.id}/like`,method:"POST"}),await o()})(n)},[e.createElementVNode("text",{class:"btn-icon"},e.toDisplayString(a.liked?"❤️":"🤍"),1),e.createElementVNode("text",{class:"btn-text"},e.toDisplayString(a.likes),1)],10,["onClick"]),e.createElementVNode("button",{class:"action-btn comment-btn",onClick:e=>(e=>{t.value[e].showComment=!t.value[e].showComment})(n)},[e.createElementVNode("text",{class:"btn-icon"},"💬"),e.createElementVNode("text",{class:"btn-text"},e.toDisplayString(a.comments.length),1)],8,["onClick"])]),a.showComment?(e.openBlock(),e.createElementBlock("view",{key:0,class:"comment-section"},[e.createElementVNode("view",{class:"comment-input-row"},[e.withDirectives(e.createElementVNode("input",{class:"comment-input","onUpdate:modelValue":e=>l.value[n]=e,placeholder:"输入你的评论..."},null,8,["onUpdate:modelValue"]),[[e.vModelText,l.value[n]]]),e.createElementVNode("button",{class:"modern-button primary comment-submit",onClick:e=>(async e=>{const a=t.value[e],n=l.value[e];n&&n.trim()&&(await d({url:"/api/comments",method:"POST",data:{post_id:a.id,content:n}}),l.value[e]="",await o())})(n)},[e.createElementVNode("text",{class:"btn-icon"},"📝")],8,["onClick"])]),a.comments.length>0?(e.openBlock(),e.createElementBlock("view",{key:0,class:"comment-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(a.comments,((a,l)=>(e.openBlock(),e.createElementBlock("view",{class:"comment-item",key:l},[e.createElementVNode("text",{class:"comment-text"},e.toDisplayString(a),1),a.startsWith("我: ")?(e.openBlock(),e.createElementBlock("button",{key:0,class:"modern-button danger comment-delete",onClick:e=>(async(e,a)=>{const n=t.value[e].comments[a];await d({url:`/api/comments/${n.id}`,method:"DELETE"}),await o()})(n,l)},[e.createElementVNode("text",{class:"btn-icon"},"🗑️")],8,["onClick"])):e.createCommentVNode("",!0)])))),128))])):e.createCommentVNode("",!0),0===a.comments.length?(e.openBlock(),e.createElementBlock("view",{key:1,class:"no-comments"},[e.createElementVNode("text",{class:"empty-text"},"暂无评论，快来抢沙发吧！")])):e.createCommentVNode("",!0)])):e.createCommentVNode("",!0)])))),128))])):e.createCommentVNode("",!0),0===t.value.length?(e.openBlock(),e.createElementBlock("view",{key:1,class:"no-posts"},[e.createElementVNode("view",{class:"empty-icon"},[e.createElementVNode("text",{class:"icon-text"},"💬")]),e.createElementVNode("text",{class:"empty-text"},"暂无动态"),e.createElementVNode("text",{class:"empty-desc"},"发布第一条学习动态吧！")])):e.createCommentVNode("",!0)])]),e.createElementVNode("view",{class:"bottom-decoration"})]))}},x={__name:"profile",setup(a){const t=e.ref({avatar:"/static/images/avatar.png",username:"自律达人",email:"<EMAIL>",bio:"热爱学习，追求进步！"}),n=()=>{c("log","at pages/profile/profile.vue:44","更换头像功能待实现")},l=()=>{c("log","at pages/profile/profile.vue:50","保存个人信息:",t.value)};return(a,o)=>(e.openBlock(),e.createElementBlock("view",{class:"profile"},[e.createElementVNode("view",{class:"header"},[e.createElementVNode("text",{class:"header-title"},"个人信息")]),e.createElementVNode("view",{class:"user-info"},[e.createElementVNode("view",{class:"avatar-container"},[e.createElementVNode("image",{src:t.value.avatar,mode:"aspectFill",class:"avatar"},null,8,["src"]),e.createElementVNode("button",{class:"edit-avatar",onClick:n},"更换头像")]),e.createElementVNode("view",{class:"info-item"},[e.createElementVNode("text",{class:"info-label"},"用户名"),e.withDirectives(e.createElementVNode("input",{class:"info-input","onUpdate:modelValue":o[0]||(o[0]=e=>t.value.username=e),placeholder:"请输入用户名"},null,512),[[e.vModelText,t.value.username]])]),e.createElementVNode("view",{class:"info-item"},[e.createElementVNode("text",{class:"info-label"},"邮箱"),e.withDirectives(e.createElementVNode("input",{class:"info-input","onUpdate:modelValue":o[1]||(o[1]=e=>t.value.email=e),placeholder:"请输入邮箱"},null,512),[[e.vModelText,t.value.email]])]),e.createElementVNode("view",{class:"info-item"},[e.createElementVNode("text",{class:"info-label"},"简介"),e.withDirectives(e.createElementVNode("textarea",{class:"info-textarea","onUpdate:modelValue":o[2]||(o[2]=e=>t.value.bio=e),placeholder:"请输入个人简介"},null,512),[[e.vModelText,t.value.bio]])]),e.createElementVNode("button",{class:"save-button",onClick:l},"保存")])]))}},y={__name:"studyrecords",setup(a){const t=e.ref([{date:"2025-03-21",title:"学习 Vue 3",duration:2.5,description:"完成了组件开发部分的学习"},{date:"2025-03-20",title:"跑步锻炼",duration:1,description:"跑了5公里，感觉很棒"}]);return(a,n)=>(e.openBlock(),e.createElementBlock("view",{class:"study-records"},[e.createElementVNode("view",{class:"header"},[e.createElementVNode("text",{class:"header-title"},"学习记录")]),e.createElementVNode("view",{class:"record-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(t.value,((a,t)=>(e.openBlock(),e.createElementBlock("view",{class:"record-item",key:t},[e.createElementVNode("view",{class:"record-date"},[e.createElementVNode("text",null,e.toDisplayString(a.date),1)]),e.createElementVNode("view",{class:"record-details"},[e.createElementVNode("text",{class:"record-title"},e.toDisplayString(a.title),1),e.createElementVNode("text",{class:"record-duration"},"时长: "+e.toDisplayString(a.duration)+"小时",1),e.createElementVNode("text",{class:"record-desc"},e.toDisplayString(a.description),1)])])))),128)),0===t.value.length?(e.openBlock(),e.createElementBlock("view",{key:0,class:"no-records"},[e.createElementVNode("text",null,"暂无学习记录")])):e.createCommentVNode("",!0)])]))}},k={__name:"setting",setup(a){const t=e.ref(!0),n=()=>{try{uni.setStorageSync("notificationEnabled",t.value.toString())}catch(e){c("error","at pages/setting/setting.vue:118","保存通知设置失败",e)}},l=e=>{t.value=e.detail.value,n(),uni.showToast({title:"通知已"+(t.value?"开启":"关闭"),icon:"success"})},o=()=>{uni.showLoading({title:"检查更新中..."}),setTimeout((()=>{uni.hideLoading(),uni.showModal({title:"检查更新",content:"当前已是最新版本：1.0.3",showCancel:!1,confirmText:"确定"})}),1e3)},r=()=>{uni.navigateTo({url:"/pages/helpfeedback/helpfeedback"})},s=()=>{uni.showModal({title:"重置数据",content:"此操作将清除所有数据（包括用户信息、好友列表、动态等），是否继续？",confirmText:"确定",cancelText:"取消",success:e=>{e.confirm&&(uni.clearStorageSync(),uni.showToast({title:"数据已重置",icon:"success"}),uni.reLaunch({url:"/pages/enter/enter"}))}})};return e.onMounted((()=>{(()=>{try{const e=uni.getStorageSync("notificationEnabled");""!==e?t.value="true"===e:(t.value=!0,n())}catch(e){c("error","at pages/setting/setting.vue:107","加载通知设置失败",e),t.value=!0,n()}})()})),(a,n)=>(e.openBlock(),e.createElementBlock("view",{class:"modern-page"},[e.createElementVNode("view",{class:"modern-header"},[e.createElementVNode("text",{class:"header-title"},"应用设置"),e.createElementVNode("text",{class:"header-subtitle"},"个性化设置，打造专属学习环境"),e.createElementVNode("view",{class:"header-decoration"})]),e.createElementVNode("view",{class:"modern-content"},[e.createElementVNode("view",{class:"modern-card settings-card fade-in-up"},[e.createElementVNode("view",{class:"card-header"},[e.createElementVNode("view",{class:"header-icon"},[e.createElementVNode("text",{class:"icon-text"},"⚙️")]),e.createElementVNode("text",{class:"card-title"},"应用设置")]),e.createElementVNode("view",{class:"settings-list"},[e.createElementVNode("view",{class:"setting-item"},[e.createElementVNode("view",{class:"setting-info"},[e.createElementVNode("view",{class:"setting-icon notification"},[e.createElementVNode("text",{class:"icon-text"},"🔔")]),e.createElementVNode("view",{class:"setting-content"},[e.createElementVNode("text",{class:"setting-label"},"通知提醒"),e.createElementVNode("text",{class:"setting-desc"},"接收学习提醒和重要通知")])]),e.createElementVNode("switch",{class:"modern-switch",checked:t.value,onChange:l},null,40,["checked"])]),e.createElementVNode("view",{class:"setting-item clickable",onClick:o},[e.createElementVNode("view",{class:"setting-info"},[e.createElementVNode("view",{class:"setting-icon update"},[e.createElementVNode("text",{class:"icon-text"},"🔄")]),e.createElementVNode("view",{class:"setting-content"},[e.createElementVNode("text",{class:"setting-label"},"检查更新"),e.createElementVNode("text",{class:"setting-desc"},"当前版本：1.0.3")])]),e.createElementVNode("text",{class:"setting-arrow"},"›")]),e.createElementVNode("view",{class:"setting-item clickable",onClick:r},[e.createElementVNode("view",{class:"setting-info"},[e.createElementVNode("view",{class:"setting-icon feedback"},[e.createElementVNode("text",{class:"icon-text"},"💬")]),e.createElementVNode("view",{class:"setting-content"},[e.createElementVNode("text",{class:"setting-label"},"评价与反馈"),e.createElementVNode("text",{class:"setting-desc"},"给我们提出建议")])]),e.createElementVNode("text",{class:"setting-arrow"},"›")]),e.createElementVNode("view",{class:"setting-item danger clickable",onClick:s},[e.createElementVNode("view",{class:"setting-info"},[e.createElementVNode("view",{class:"setting-icon reset"},[e.createElementVNode("text",{class:"icon-text"},"🗑️")]),e.createElementVNode("view",{class:"setting-content"},[e.createElementVNode("text",{class:"setting-label danger-text"},"重置数据"),e.createElementVNode("text",{class:"setting-desc danger-desc"},"将清除所有数据，请谨慎操作")])]),e.createElementVNode("text",{class:"setting-arrow danger-arrow"},"›")])])])]),e.createElementVNode("view",{class:"bottom-decoration"})]))}},C={__name:"helpfeedback",setup(a){const t=e.ref(0),n=e.ref(""),l=e.ref([]),o=()=>{try{uni.setStorageSync("rating",t.value.toString()),uni.setStorageSync("feedbackRecords",JSON.stringify(l.value))}catch(e){c("error","at pages/helpfeedback/helpfeedback.vue:99","保存数据失败",e)}},r=()=>{if(!n.value.trim())return void uni.showToast({title:"请输入反馈内容",icon:"none"});const e={rating:t.value,text:n.value,time:(new Date).toLocaleString(),expanded:!1};l.value.unshift(e),n.value="",o(),uni.showToast({title:"提交成功",icon:"success"})};return e.onMounted((()=>{(()=>{try{const e=uni.getStorageSync("rating"),a=uni.getStorageSync("feedbackRecords");if(e&&(t.value=Number(e)),a){const e=JSON.parse(a);Array.isArray(e)?l.value=e:l.value=[]}else l.value=[]}catch(e){c("error","at pages/helpfeedback/helpfeedback.vue:87","加载数据失败",e),l.value=[],t.value=0}})()})),(a,c)=>(e.openBlock(),e.createElementBlock("view",{class:"feedback-page"},[e.createElementVNode("view",{class:"section-title"},[e.createElementVNode("text",null,"评价与反馈")]),e.createElementVNode("view",{class:"rating-section"},[e.createElementVNode("view",{class:"rating-title"},"请给我们评分"),e.createElementVNode("view",{class:"stars-container"},[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(5,(a=>e.createElementVNode("text",{key:a,class:e.normalizeClass(["star",{active:a<=t.value}]),onClick:e=>{return n=a,t.value=n,void o();var n}},"★",10,["onClick"]))),64))])]),e.createElementVNode("view",{class:"feedback-section"},[e.withDirectives(e.createElementVNode("textarea",{"onUpdate:modelValue":c[0]||(c[0]=e=>n.value=e),class:"feedback-input",placeholder:"请输入您的宝贵意见..."},null,512),[[e.vModelText,n.value]]),e.createElementVNode("button",{class:"submit-button",onClick:r},[e.createElementVNode("text",{class:"button-text"},"提交反馈")])]),e.createElementVNode("view",{class:"history-section"},[e.createElementVNode("view",{class:"section-title"},[e.createElementVNode("text",null,"反馈历史")]),(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(l.value,((a,t)=>(e.openBlock(),e.createElementBlock("view",{key:t,class:"history-item"},[e.createElementVNode("view",{class:"history-header",onClick:e=>(e=>{l.value[e]&&(l.value[e].expanded=!l.value[e].expanded,o())})(t)},[e.createElementVNode("text",null,"反馈 #"+e.toDisplayString(t+1)+" - "+e.toDisplayString(a.time),1),e.createElementVNode("text",{class:"toggle-icon"},e.toDisplayString(a.expanded?"▲":"▼"),1)],8,["onClick"]),a.expanded?(e.openBlock(),e.createElementBlock("view",{key:0,class:"history-content",onClick:e=>(e=>{uni.showModal({title:`反馈详情 - ${e.time}`,content:`评分: ${e.rating} 星\n内容: ${e.text}`,showCancel:!1,confirmText:"关闭"})})(a)},[e.createElementVNode("text",null,"评分: "+e.toDisplayString(a.rating)+" 星",1),e.createElementVNode("text",null,"内容: "+e.toDisplayString(a.text),1)],8,["onClick"])):e.createCommentVNode("",!0)])))),128))])]))}},S={__name:"login",setup(a){const t=e.ref(!1),n=e.ref(""),l=e.ref(""),o=e.ref(""),r=e.ref(!1),s=e.ref({name:"中国🇨🇳:+86",code:"+86"}),d=e.computed((()=>{const e=s.value.name.split(":");return`${e[0].replace(/[^🇨🇳🇺🇳🇺🇸🇬🇧🇯🇵🇰🇷🇩🇪🇫🇷🇮🇹🇪🇸🇷🇺🇮🇳🇧🇷🇦🇺🇨🇦🇲🇽🇿🇦🇸🇬🇹🇭🇻🇳🇵🇭🇲🇾🇮🇩🇹🇷🇸🇦🇦🇪🇪🇬🇳🇬🇰🇪🇬🇭🇦🇷🇨🇴🇵🇪🇨🇱🇻🇪🇪🇨🇧🇴🇵🇾🇺🇾🇨🇺🇩🇴🇭🇹🇬🇹🇭🇳🇳🇮🇨🇷🇵🇦🇸🇻🇯🇲🇹🇹🇧🇸🇧🇧🇬🇾🇸🇷🇧🇿🇰🇳🇱🇨🇻🇨🇬🇩🇦🇬🇩🇲🇰🇾🇧🇲🇻🇬🇹🇨🇦🇼🇨🇼🇸🇽🇧🇶🇬🇱🇫🇴🇮🇸🇳🇴🇸🇪🇫🇮🇩🇰]/g,"")}:${e[1]||s.value.code}`}));e.onMounted((()=>{const e=uni.getStorageSync("selectedRegion");e&&(s.value=e),uni.$on("updateRegion",i)})),e.onUnmounted((()=>{uni.$off("updateRegion",i)}));const i=e=>{s.value=e,uni.setStorageSync("selectedRegion",e)},m=()=>{uni.navigateTo({url:`/pages/nation/nation?selectedRegion=${JSON.stringify(s.value)}`})},u=()=>{if(!l.value.trim()||!n.value.trim())return void uni.showToast({title:"请输入手机号和新密码",icon:"none"});const e=`${s.value.code}-${l.value}`;c("log","at pages/login/login.vue:178","注册/登录/重置密码 username:",e),uni.request({url:"http://localhost:5000/api/reset_pwd",method:"POST",data:{username:e,new_password:n.value},success:e=>{"密码重置成功"===e.data.msg?(uni.showToast({title:"重置成功",icon:"success"}),t.value=!1,n.value=""):uni.showToast({title:e.data.msg||"重置失败",icon:"none"})},fail:()=>{uni.showToast({title:"网络错误",icon:"none"})}})},N=()=>{if(!l.value.trim()||!o.value.trim())return void uni.showToast({title:"请输入手机号和密码",icon:"none"});const e=`${s.value.code}-${l.value}`;uni.request({url:"http://localhost:5000/api/register",method:"POST",data:{username:e,password:o.value},success:a=>{"注册成功"===a.data.msg||"用户名已存在"===a.data.msg?uni.request({url:"http://localhost:5000/api/login",method:"POST",data:{username:e,password:o.value},success:e=>{e.data.token?(uni.setStorageSync("token",e.data.token),uni.setStorageSync("loginData",{isLoggedIn:!0}),uni.request({url:"http://localhost:5000/api/me",method:"GET",header:{Authorization:"Bearer "+e.data.token},success:e=>{e.data.username&&uni.setStorageSync("userData",e.data),uni.showToast({title:"登录成功",icon:"success"}),setTimeout((()=>{uni.reLaunch({url:"/pages/enter/enter"})}),1e3)},fail:()=>{uni.showToast({title:"获取用户信息失败",icon:"none"})}})):uni.showToast({title:e.data.msg||"登录失败",icon:"none"})},fail:()=>{uni.showToast({title:"网络错误",icon:"none"})}}):uni.showToast({title:a.data.msg||"注册失败",icon:"none"})},fail:()=>{uni.showToast({title:"网络错误",icon:"none"})}})},v=()=>{r.value=!r.value},E=()=>{uni.showModal({title:"服务条款",content:"欢迎使用自律助手！在使用本应用前，请仔细阅读以下条款：\n\n1. 用户需遵守所有适用的法律法规，不得利用本应用从事任何非法活动。\n2. 本应用提供的所有内容仅供参考，用户需自行承担使用后果。\n3. 我们有权随时更新或修改服务条款，更新后将通过应用内通知用户。\n4. 用户不得以任何方式干扰本应用的正常运行，包括但不限于传播病毒、恶意攻击等。\n5. 如有任何争议，将按照中华人民共和国法律解决。\n\n若您继续使用本应用，即表示您已同意上述条款。",showCancel:!1,confirmText:"我已阅读"})},g=()=>{uni.showModal({title:"隐私政策",content:"我们非常重视您的隐私，以下是自律助手的隐私政策：\n\n1. 我们可能会收集您的用户名、手机号、邮箱等信息，用于提供和优化服务。\n2. 您的个人信息将受到严格保护，不会出售或未经授权分享给第三方。\n3. 我们使用安全技术（如加密）保护您的数据，但无法完全保证数据绝对安全。\n4. 您有权随时查看、修改或删除您的个人信息，具体操作可通过设置页面完成。\n5. 本应用可能会使用第三方服务（如第三方登录），其隐私政策由相应服务提供商负责。\n\n若您继续使用本应用，即表示您已同意上述隐私政策。",showCancel:!1,confirmText:"我已阅读"})};return(a,c)=>{const s=e.resolveComponent("uni-popup");return e.openBlock(),e.createElementBlock("view",{class:"modern-page login-page"},[e.createElementVNode("view",{class:"modern-header"},[e.createElementVNode("text",{class:"header-title"},"自律助手"),e.createElementVNode("text",{class:"header-subtitle"},"登录您的账户，开启学习之旅"),e.createElementVNode("view",{class:"header-decoration"})]),e.createElementVNode("view",{class:"modern-content"},[e.createElementVNode("view",{class:"modern-card login-card fade-in-up"},[e.createElementVNode("view",{class:"card-header"},[e.createElementVNode("view",{class:"header-icon"},[e.createElementVNode("text",{class:"icon-text"},"🔑")]),e.createElementVNode("text",{class:"card-title"},"手机号登录/注册"),e.createElementVNode("text",{class:"card-subtitle"},"请输入您的手机号以登录/注册")]),e.createElementVNode("view",{class:"login-form"},[e.createElementVNode("view",{class:"form-group"},[e.createElementVNode("text",{class:"form-label"},"手机号"),e.createElementVNode("view",{class:"phone-input-container"},[e.createElementVNode("button",{class:"region-selector",onClick:m},[e.createElementVNode("text",{class:"region-text"},e.toDisplayString(d.value),1),e.createElementVNode("text",{class:"selector-arrow"},"▼")]),e.withDirectives(e.createElementVNode("input",{class:"modern-input phone-input","onUpdate:modelValue":c[0]||(c[0]=e=>l.value=e),placeholder:"请输入手机号",type:"number"},null,512),[[e.vModelText,l.value]])])]),e.createElementVNode("view",{class:"form-group"},[e.createElementVNode("text",{class:"form-label"},"密码"),e.createElementVNode("view",{class:"password-input-container"},[e.withDirectives(e.createElementVNode("input",{class:"modern-input password-input","onUpdate:modelValue":c[1]||(c[1]=e=>o.value=e),type:r.value?"text":"password",placeholder:"请输入密码"},null,8,["type"]),[[e.vModelDynamic,o.value]]),e.createElementVNode("button",{class:"password-toggle",onClick:v},[e.createElementVNode("text",{class:"toggle-icon"},e.toDisplayString(r.value?"👁️":"👁️‍🗨️"),1)])])]),e.createElementVNode("button",{class:"modern-button primary login-btn",onClick:N},[e.createElementVNode("text",{class:"btn-icon"},"🚀"),e.createElementVNode("text",{class:"btn-text"},"继续")]),e.createElementVNode("button",{class:"forget-password-btn",onClick:c[2]||(c[2]=e=>t.value=!0)}," 忘记密码？ "),e.createElementVNode("view",{class:"terms-section"},[e.createElementVNode("text",{class:"terms-text"},[e.createTextVNode(" 点击继续即表示您同意我们的 "),e.createElementVNode("text",{class:"terms-link",onClick:E},"服务条款"),e.createTextVNode(" 和 "),e.createElementVNode("text",{class:"terms-link",onClick:g},"隐私政策")])])])])]),e.createElementVNode("view",{class:"bottom-decoration"}),e.createVNode(s,{modelValue:t.value,"onUpdate:modelValue":c[5]||(c[5]=e=>t.value=e),type:"dialog"},{default:e.withCtx((()=>[e.createElementVNode("view",{class:"modern-modal reset-pwd-modal"},[e.createElementVNode("view",{class:"modal-header"},[e.createElementVNode("text",{class:"modal-title"},"重置密码")]),e.createElementVNode("view",{class:"modal-body"},[e.withDirectives(e.createElementVNode("input",{class:"modern-input","onUpdate:modelValue":c[3]||(c[3]=e=>n.value=e),placeholder:"请输入新密码",type:"password"},null,512),[[e.vModelText,n.value]])]),e.createElementVNode("view",{class:"modal-footer"},[e.createElementVNode("button",{class:"modern-button secondary",onClick:c[4]||(c[4]=e=>t.value=!1)},"取消"),e.createElementVNode("button",{class:"modern-button primary",onClick:u},"重置密码")])])])),_:1},8,["modelValue"])])}}},b={__name:"nation",setup(a){const t=e.ref([{name:"中国",flag:"🇨🇳",enName:"China",code:"CN",dialCode:"+86"},{name:"阿布哈兹",flag:"🇬🇪",enName:"Abkhazia",code:"GE",dialCode:"+995"},{name:"阿富汗",flag:"🇦🇫",enName:"Afghanistan",code:"AF",dialCode:"+93"},{name:"阿尔巴尼亚",flag:"🇦🇱",enName:"Albania",code:"AL",dialCode:"+355"},{name:"阿尔及利亚",flag:"🇩🇿",enName:"Algeria",code:"DZ",dialCode:"+213"},{name:"安道尔",flag:"🇦🇩",enName:"Andorra",code:"AD",dialCode:"+376"},{name:"安哥拉",flag:"🇦🇴",enName:"Angola",code:"AO",dialCode:"+244"},{name:"安提瓜和巴布达",flag:"🇦🇬",enName:"Antigua and Barbuda",code:"AG",dialCode:"+1-268"},{name:"阿根廷",flag:"🇦🇷",enName:"Argentina",code:"AR",dialCode:"+54"},{name:"亚美尼亚",flag:"🇦🇲",enName:"Armenia",code:"AM",dialCode:"+374"},{name:"阿鲁巴",flag:"🇦🇼",enName:"Aruba",code:"AW",dialCode:"+297"},{name:"澳大利亚",flag:"🇦🇺",enName:"Australia",code:"AU",dialCode:"+61"},{name:"奥地利",flag:"🇦🇹",enName:"Austria",code:"AT",dialCode:"+43"},{name:"阿塞拜疆",flag:"🇦🇿",enName:"Azerbaijan",code:"AZ",dialCode:"+994"},{name:"巴哈马",flag:"🇧🇸",enName:"Bahamas",code:"BS",dialCode:"+1-242"},{name:"巴林",flag:"🇧🇭",enName:"Bahrain",code:"BH",dialCode:"+973"},{name:"孟加拉国",flag:"🇧🇩",enName:"Bangladesh",code:"BD",dialCode:"+880"},{name:"巴巴多斯",flag:"🇧🇧",enName:"Barbados",code:"BB",dialCode:"+1-246"},{name:"白俄罗斯",flag:"🇧🇾",enName:"Belarus",code:"BY",dialCode:"+375"},{name:"比利时",flag:"🇧🇪",enName:"Belgium",code:"BE",dialCode:"+32"},{name:"伯利兹",flag:"🇧🇿",enName:"Belize",code:"BZ",dialCode:"+501"},{name:"贝宁",flag:"🇧🇯",enName:"Benin",code:"BJ",dialCode:"+229"},{name:"百慕大",flag:"🇧🇲",enName:"Bermuda",code:"BM",dialCode:"+1-441"},{name:"不丹",flag:"🇧🇹",enName:"Bhutan",code:"BT",dialCode:"+975"},{name:"玻利维亚",flag:"🇧🇴",enName:"Bolivia",code:"BO",dialCode:"+591"},{name:"波斯尼亚和黑塞哥维那",flag:"🇧🇦",enName:"Bosnia and Herzegovina",code:"BA",dialCode:"+387"},{name:"博茨瓦纳",flag:"🇧🇼",enName:"Botswana",code:"BW",dialCode:"+267"},{name:"巴西",flag:"🇧🇷",enName:"Brazil",code:"BR",dialCode:"+55"},{name:"文莱",flag:"🇧🇳",enName:"Brunei",code:"BN",dialCode:"+673"},{name:"保加利亚",flag:"🇧🇬",enName:"Bulgaria",code:"BG",dialCode:"+359"},{name:"布基纳法索",flag:"🇧🇫",enName:"Burkina Faso",code:"BF",dialCode:"+226"},{name:"布隆迪",flag:"🇧🇮",enName:"Burundi",code:"BI",dialCode:"+257"},{name:"佛得角",flag:"🇨🇻",enName:"Cabo Verde",code:"CV",dialCode:"+238"},{name:"柬埔寨",flag:"🇰🇭",enName:"Cambodia",code:"KH",dialCode:"+855"},{name:"喀麦隆",flag:"🇨🇲",enName:"Cameroon",code:"CM",dialCode:"+237"},{name:"加拿大",flag:"🇨🇦",enName:"Canada",code:"CA",dialCode:"+1"},{name:"开曼群岛",flag:"🇰🇾",enName:"Cayman Islands",code:"KY",dialCode:"+1-345"},{name:"中非共和国",flag:"🇨🇫",enName:"Central African Republic",code:"CF",dialCode:"+236"},{name:"乍得",flag:"🇹🇩",enName:"Chad",code:"TD",dialCode:"+235"},{name:"智利",flag:"🇨🇱",enName:"Chile",code:"CL",dialCode:"+56"},{name:"哥伦比亚",flag:"🇨🇴",enName:"Colombia",code:"CO",dialCode:"+57"},{name:"科摩罗",flag:"🇰🇲",enName:"Comoros",code:"KM",dialCode:"+269"},{name:"刚果（布）",flag:"🇨🇬",enName:"Congo (Brazzaville)",code:"CG",dialCode:"+242"},{name:"刚果（金）",flag:"🇨🇩",enName:"Congo (Kinshasa)",code:"CD",dialCode:"+243"},{name:"库克群岛",flag:"🇨🇰",enName:"Cook Islands",code:"CK",dialCode:"+682"},{name:"哥斯达黎加",flag:"🇨🇷",enName:"Costa Rica",code:"CR",dialCode:"+506"},{name:"科特迪瓦",flag:"🇨🇮",enName:"Côte d'Ivoire",code:"CI",dialCode:"+225"},{name:"克罗地亚",flag:"🇭🇷",enName:"Croatia",code:"HR",dialCode:"+385"},{name:"古巴",flag:"🇨🇺",enName:"Cuba",code:"CU",dialCode:"+53"},{name:"库拉索",flag:"🇨🇼",enName:"Curaçao",code:"CW",dialCode:"+599"},{name:"塞浦路斯",flag:"🇨🇾",enName:"Cyprus",code:"CY",dialCode:"+357"},{name:"捷克共和国",flag:"🇨🇿",enName:"Czech Republic",code:"CZ",dialCode:"+420"},{name:"丹麦",flag:"🇩🇰",enName:"Denmark",code:"DK",dialCode:"+45"},{name:"吉布提",flag:"🇩🇯",enName:"Djibouti",code:"DJ",dialCode:"+253"},{name:"多米尼克",flag:"🇩🇲",enName:"Dominica",code:"DM",dialCode:"+1-767"},{name:"多米尼加共和国",flag:"🇩🇴",enName:"Dominican Republic",code:"DO",dialCode:"+1-809"},{name:"厄瓜多尔",flag:"🇪🇨",enName:"Ecuador",code:"EC",dialCode:"+593"},{name:"埃及",flag:"🇪🇬",enName:"Egypt",code:"EG",dialCode:"+20"},{name:"萨尔瓦多",flag:"🇸🇻",enName:"El Salvador",code:"SV",dialCode:"+503"},{name:"赤道几内亚",flag:"🇬🇶",enName:"Equatorial Guinea",code:"GQ",dialCode:"+240"},{name:"厄立特里亚",flag:"🇪🇷",enName:"Eritrea",code:"ER",dialCode:"+291"},{name:"爱沙尼亚",flag:"🇪🇪",enName:"Estonia",code:"EE",dialCode:"+372"},{name:"埃斯瓦蒂尼",flag:"🇸🇿",enName:"Eswatini",code:"SZ",dialCode:"+268"},{name:"埃塞俄比亚",flag:"🇪🇹",enName:"Ethiopia",code:"ET",dialCode:"+251"},{name:"斐济",flag:"🇫🇯",enName:"Fiji",code:"FJ",dialCode:"+679"},{name:"芬兰",flag:"🇫🇮",enName:"Finland",code:"FI",dialCode:"+358"},{name:"法国",flag:"🇫🇷",enName:"France",code:"FR",dialCode:"+33"},{name:"加蓬",flag:"🇬🇦",enName:"Gabon",code:"GA",dialCode:"+241"},{name:"冈比亚",flag:"🇬🇲",enName:"Gambia",code:"GM",dialCode:"+220"},{name:"格鲁吉亚",flag:"🇬🇪",enName:"Georgia",code:"GE",dialCode:"+995"},{name:"德国",flag:"🇩🇪",enName:"Germany",code:"DE",dialCode:"+49"},{name:"加纳",flag:"🇬🇭",enName:"Ghana",code:"GH",dialCode:"+233"},{name:"希腊",flag:"🇬🇷",enName:"Greece",code:"GR",dialCode:"+30"},{name:"格陵兰",flag:"🇬🇱",enName:"Greenland",code:"GL",dialCode:"+299"},{name:"格林纳达",flag:"🇬🇩",enName:"Grenada",code:"GD",dialCode:"+1-473"},{name:"关岛",flag:"🇬🇺",enName:"Guam",code:"GU",dialCode:"+1-671"},{name:"危地马拉",flag:"🇬🇹",enName:"Guatemala",code:"GT",dialCode:"+502"},{name:"几内亚",flag:"🇬🇳",enName:"Guinea",code:"GN",dialCode:"+224"},{name:"几内亚比绍",flag:"🇬🇼",enName:"Guinea-Bissau",code:"GW",dialCode:"+245"},{name:"圭亚那",flag:"🇬🇾",enName:"Guyana",code:"GY",dialCode:"+592"},{name:"海地",flag:"🇭🇹",enName:"Haiti",code:"HT",dialCode:"+509"},{name:"洪都拉斯",flag:"🇭🇳",enName:"Honduras",code:"HN",dialCode:"+504"},{name:"香港",flag:"🇭🇰",enName:"Hong Kong",code:"HK",dialCode:"+852"},{name:"匈牙利",flag:"🇭🇺",enName:"Hungary",code:"HU",dialCode:"+36"},{name:"冰岛",flag:"🇮🇸",enName:"Iceland",code:"IS",dialCode:"+354"},{name:"印度",flag:"🇮🇳",enName:"India",code:"IN",dialCode:"+91"},{name:"印度尼西亚",flag:"🇮🇩",enName:"Indonesia",code:"ID",dialCode:"+62"},{name:"伊朗",flag:"🇮🇷",enName:"Iran",code:"IR",dialCode:"+98"},{name:"伊拉克",flag:"🇮🇶",enName:"Iraq",code:"IQ",dialCode:"+964"},{name:"爱尔兰",flag:"🇮🇪",enName:"Ireland",code:"IE",dialCode:"+353"},{name:"以色列",flag:"🇮🇱",enName:"Israel",code:"IL",dialCode:"+972"},{name:"意大利",flag:"🇮🇹",enName:"Italy",code:"IT",dialCode:"+39"},{name:"牙买加",flag:"🇯🇲",enName:"Jamaica",code:"JM",dialCode:"+1-876"},{name:"日本",flag:"🇯🇵",enName:"Japan",code:"JP",dialCode:"+81"},{name:"约旦",flag:"🇯🇴",enName:"Jordan",code:"JO",dialCode:"+962"},{name:"哈萨克斯坦",flag:"🇰🇿",enName:"Kazakhstan",code:"KZ",dialCode:"+7"},{name:"肯尼亚",flag:"🇰🇪",enName:"Kenya",code:"KE",dialCode:"+254"},{name:"基里巴斯",flag:"🇰🇮",enName:"Kiribati",code:"KI",dialCode:"+686"},{name:"科索沃",flag:"🇽🇰",enName:"Kosovo",code:"XK",dialCode:"+383"},{name:"科威特",flag:"🇰🇼",enName:"Kuwait",code:"KW",dialCode:"+965"},{name:"吉尔吉斯斯坦",flag:"🇰🇬",enName:"Kyrgyzstan",code:"KG",dialCode:"+996"},{name:"老挝",flag:"🇱🇦",enName:"Laos",code:"LA",dialCode:"+856"},{name:"拉脱维亚",flag:"🇱🇻",enName:"Latvia",code:"LV",dialCode:"+371"},{name:"黎巴嫩",flag:"🇱🇧",enName:"Lebanon",code:"LB",dialCode:"+961"},{name:"莱索托",flag:"🇱🇸",enName:"Lesotho",code:"LS",dialCode:"+266"},{name:"利比里亚",flag:"🇱🇷",enName:"Liberia",code:"LR",dialCode:"+231"},{name:"利比亚",flag:"🇱🇾",enName:"Libya",code:"LY",dialCode:"+218"},{name:"列支敦士登",flag:"🇱🇮",enName:"Liechtenstein",code:"LI",dialCode:"+423"},{name:"立陶宛",flag:"🇱🇹",enName:"Lithuania",code:"LT",dialCode:"+370"},{name:"卢森堡",flag:"🇱🇺",enName:"Luxembourg",code:"LU",dialCode:"+352"},{name:"澳门",flag:"🇲🇴",enName:"Macao",code:"MO",dialCode:"+853"},{name:"马达加斯加",flag:"🇲🇬",enName:"Madagascar",code:"MG",dialCode:"+261"},{name:"马拉维",flag:"🇲🇼",enName:"Malawi",code:"MW",dialCode:"+265"},{name:"马来西亚",flag:"🇲🇾",enName:"Malaysia",code:"MY",dialCode:"+60"},{name:"马尔代夫",flag:"🇲🇻",enName:"Maldives",code:"MV",dialCode:"+960"},{name:"马里",flag:"🇲🇱",enName:"Mali",code:"ML",dialCode:"+223"},{name:"马耳他",flag:"🇲🇹",enName:"Malta",code:"MT",dialCode:"+356"},{name:"马绍尔群岛",flag:"🇲🇭",enName:"Marshall Islands",code:"MH",dialCode:"+692"},{name:"毛里塔尼亚",flag:"🇲🇷",enName:"Mauritania",code:"MR",dialCode:"+222"},{name:"毛里求斯",flag:"🇲🇺",enName:"Mauritius",code:"MU",dialCode:"+230"},{name:"墨西哥",flag:"🇲🇽",enName:"Mexico",code:"MX",dialCode:"+52"},{name:"密克罗尼西亚",flag:"🇫🇲",enName:"Micronesia",code:"FM",dialCode:"+691"},{name:"摩尔多瓦",flag:"🇲🇩",enName:"Moldova",code:"MD",dialCode:"+373"},{name:"摩纳哥",flag:"🇲🇨",enName:"Monaco",code:"MC",dialCode:"+377"},{name:"蒙古",flag:"🇲🇳",enName:"Mongolia",code:"MN",dialCode:"+976"},{name:"黑山",flag:"🇲🇪",enName:"Montenegro",code:"ME",dialCode:"+382"},{name:"摩洛哥",flag:"🇲🇦",enName:"Morocco",code:"MA",dialCode:"+212"},{name:"莫桑比克",flag:"🇲🇿",enName:"Mozambique",code:"MZ",dialCode:"+258"},{name:"缅甸",flag:"🇲🇲",enName:"Myanmar",code:"MM",dialCode:"+95"},{name:"纳米比亚",flag:"🇳🇦",enName:"Namibia",code:"NA",dialCode:"+264"},{name:"瑙鲁",flag:"🇳🇷",enName:"Nauru",code:"NR",dialCode:"+674"},{name:"尼泊尔",flag:"🇳🇵",enName:"Nepal",code:"NP",dialCode:"+977"},{name:"荷兰",flag:"🇳🇱",enName:"Netherlands",code:"NL",dialCode:"+31"},{name:"新西兰",flag:"🇳🇿",enName:"New Zealand",code:"NZ",dialCode:"+64"},{name:"尼加拉瓜",flag:"🇳🇮",enName:"Nicaragua",code:"NI",dialCode:"+505"},{name:"尼日尔",flag:"🇳🇪",enName:"Niger",code:"NE",dialCode:"+227"},{name:"尼日利亚",flag:"🇳🇬",enName:"Nigeria",code:"NG",dialCode:"+234"},{name:"纽埃",flag:"🇳🇺",enName:"Niue",code:"NU",dialCode:"+683"},{name:"北马其顿",flag:"🇲🇰",enName:"North Macedonia",code:"MK",dialCode:"+389"},{name:"北塞浦路斯",flag:"🇹🇷",enName:"Northern Cyprus",code:"TR",dialCode:"+90-392"},{name:"挪威",flag:"🇳🇴",enName:"Norway",code:"NO",dialCode:"+47"},{name:"阿曼",flag:"🇴🇲",enName:"Oman",code:"OM",dialCode:"+968"},{name:"巴基斯坦",flag:"🇵🇰",enName:"Pakistan",code:"PK",dialCode:"+92"},{name:"帕劳",flag:"🇵🇼",enName:"Palau",code:"PW",dialCode:"+680"},{name:"巴勒斯坦",flag:"🇵🇸",enName:"Palestine",code:"PS",dialCode:"+970"},{name:"巴拿马",flag:"🇵🇦",enName:"Panama",code:"PA",dialCode:"+507"},{name:"巴布亚新几内亚",flag:"🇵🇬",enName:"Papua New Guinea",code:"PG",dialCode:"+675"},{name:"巴拉圭",flag:"🇵🇾",enName:"Paraguay",code:"PY",dialCode:"+595"},{name:"秘鲁",flag:"🇵🇪",enName:"Peru",code:"PE",dialCode:"+51"},{name:"菲律宾",flag:"🇵🇭",enName:"Philippines",code:"PH",dialCode:"+63"},{name:"波兰",flag:"🇵🇱",enName:"Poland",code:"PL",dialCode:"+48"},{name:"葡萄牙",flag:"🇵🇹",enName:"Portugal",code:"PT",dialCode:"+351"},{name:"波多黎各",flag:"🇵🇷",enName:"Puerto Rico",code:"PR",dialCode:"+1-787"},{name:"卡塔尔",flag:"🇶🇦",enName:"Qatar",code:"QA",dialCode:"+974"},{name:"罗马尼亚",flag:"🇷🇴",enName:"Romania",code:"RO",dialCode:"+40"},{name:"俄罗斯",flag:"🇷🇺",enName:"Russia",code:"RU",dialCode:"+7"},{name:"卢旺达",flag:"🇷🇼",enName:"Rwanda",code:"RW",dialCode:"+250"},{name:"圣基茨和尼维斯",flag:"🇰🇳",enName:"Saint Kitts and Nevis",code:"KN",dialCode:"+1-869"},{name:"圣卢西亚",flag:"🇱🇨",enName:"Saint Lucia",code:"LC",dialCode:"+1-758"},{name:"圣文森特和格林纳丁斯",flag:"🇻🇚",enName:"Saint Vincent and the Grenadines",code:"VC",dialCode:"+1-784"},{name:"萨摩亚",flag:"🇼🇸",enName:"Samoa",code:"WS",dialCode:"+685"},{name:"圣马力诺",flag:"🇸🇲",enName:"San Marino",code:"SM",dialCode:"+378"},{name:"圣多美和普林西比",flag:"🇸🇹",enName:"São Tomé and Príncipe",code:"ST",dialCode:"+239"},{name:"沙特阿拉伯",flag:"🇸🇦",enName:"Saudi Arabia",code:"SA",dialCode:"+966"},{name:"塞内加尔",flag:"🇸🇳",enName:"Senegal",code:"SN",dialCode:"+221"},{name:"塞尔维亚",flag:"🇷🇸",enName:"Serbia",code:"RS",dialCode:"+381"},{name:"塞舌尔",flag:"🇸🇨",enName:"Seychelles",code:"SC",dialCode:"+248"},{name:"塞拉利昂",flag:"🇸🇱",enName:"Sierra Leone",code:"SL",dialCode:"+232"},{name:"新加坡",flag:"🇸🇬",enName:"Singapore",code:"SG",dialCode:"+65"},{name:"圣马丁（荷兰部分）",flag:"🇸🇽",enName:"Sint Maarten",code:"SX",dialCode:"+1-721"},{name:"斯洛伐克",flag:"🇸🇰",enName:"Slovakia",code:"SK",dialCode:"+421"},{name:"斯洛文尼亚",flag:"🇸🇮",enName:"Slovenia",code:"SI",dialCode:"+386"},{name:"所罗门群岛",flag:"🇸🇧",enName:"Solomon Islands",code:"SB",dialCode:"+677"},{name:"索马里",flag:"🇸🇴",enName:"Somalia",code:"SO",dialCode:"+252"},{name:"索马里兰",flag:"🇸🇴",enName:"Somaliland",code:"SO",dialCode:"+252"},{name:"南非",flag:"🇿🇦",enName:"South Africa",code:"ZA",dialCode:"+27"},{name:"南奥塞梯",flag:"🇬🇪",enName:"South Ossetia",code:"GE",dialCode:"+995"},{name:"南苏丹",flag:"🇸🇸",enName:"South Sudan",code:"SS",dialCode:"+211"},{name:"西班牙",flag:"🇪🇸",enName:"Spain",code:"ES",dialCode:"+34"},{name:"斯里兰卡",flag:"🇱🇰",enName:"Sri Lanka",code:"LK",dialCode:"+94"},{name:"苏丹",flag:"🇸🇩",enName:"Sudan",code:"SD",dialCode:"+249"},{name:"苏里南",flag:"🇸🇷",enName:"Suriname",code:"SR",dialCode:"+597"},{name:"瑞典",flag:"🇸🇪",enName:"Sweden",code:"SE",dialCode:"+46"},{name:"瑞士",flag:"🇨🇭",enName:"Switzerland",code:"CH",dialCode:"+41"},{name:"叙利亚",flag:"🇸🇾",enName:"Syria",code:"SY",dialCode:"+963"},{name:"台湾",flag:"🇹🇼",enName:"Taiwan",code:"TW",dialCode:"+886"},{name:"塔吉克斯坦",flag:"🇹🇯",enName:"Tajikistan",code:"TJ",dialCode:"+992"},{name:"坦桑尼亚",flag:"🇹🇿",enName:"Tanzania",code:"TZ",dialCode:"+255"},{name:"泰国",flag:"🇹🇭",enName:"Thailand",code:"TH",dialCode:"+66"},{name:"东帝汶",flag:"🇹🇱",enName:"Timor-Leste",code:"TL",dialCode:"+670"},{name:"多哥",flag:"🇹🇬",enName:"Togo",code:"TG",dialCode:"+228"},{name:"托克劳",flag:"🇹🇰",enName:"Tokelau",code:"TK",dialCode:"+690"},{name:"汤加",flag:"🇹🇴",enName:"Tonga",code:"TO",dialCode:"+676"},{name:"特立尼达和多巴哥",flag:"🇹🇹",enName:"Trinidad and Tobago",code:"TT",dialCode:"+1-868"},{name:"突尼斯",flag:"🇹🇳",enName:"Tunisia",code:"TN",dialCode:"+216"},{name:"土耳其",flag:"🇹🇷",enName:"Turkey",code:"TR",dialCode:"+90"},{name:"土库曼斯坦",flag:"🇹🇲",enName:"Turkmenistan",code:"TM",dialCode:"+993"},{name:"图瓦卢",flag:"🇹🇻",enName:"Tuvalu",code:"TV",dialCode:"+688"},{name:"乌干达",flag:"🇺🇬",enName:"Uganda",code:"UG",dialCode:"+256"},{name:"乌克兰",flag:"🇺🇦",enName:"Ukraine",code:"UA",dialCode:"+380"},{name:"阿联酋",flag:"🇦🇪",enName:"United Arab Emirates",code:"AE",dialCode:"+971"},{name:"英国",flag:"🇬🇧",enName:"United Kingdom",code:"GB",dialCode:"+44"},{name:"美国",flag:"🇺🇸",enName:"United States",code:"US",dialCode:"+1"},{name:"乌拉圭",flag:"🇺🇾",enName:"Uruguay",code:"UY",dialCode:"+598"},{name:"乌兹别克斯坦",flag:"🇺🇿",enName:"Uzbekistan",code:"UZ",dialCode:"+998"},{name:"瓦努阿图",flag:"🇻🇺",enName:"Vanuatu",code:"VU",dialCode:"+678"},{name:"梵蒂冈",flag:"🇻🇦",enName:"Vatican City",code:"VA",dialCode:"+39"},{name:"委内瑞拉",flag:"🇻🇪",enName:"Venezuela",code:"VE",dialCode:"+58"},{name:"越南",flag:"🇻🇳",enName:"Vietnam",code:"VN",dialCode:"+84"},{name:"也门",flag:"🇾🇪",enName:"Yemen",code:"YE",dialCode:"+967"},{name:"赞比亚",flag:"🇿🇲",enName:"Zambia",code:"ZM",dialCode:"+260"},{name:"津巴布韦",flag:"🇿🇼",enName:"Zimbabwe",code:"ZW",dialCode:"+263"}]),n=e.ref(""),l=e.ref([]),o=e.ref("zh");e.onMounted((()=>{l.value=t.value}));const c=e=>{o.value=e,n.value="",l.value=t.value},r=()=>{const e=n.value.trim().toLowerCase();l.value=e?t.value.filter((a=>"zh"===o.value?a.name.toLowerCase().includes(e):a.enName.toLowerCase().includes(e))):t.value};return(a,t)=>(e.openBlock(),e.createElementBlock("view",{class:"modern-page"},[e.createElementVNode("view",{class:"modern-header"},[e.createElementVNode("text",{class:"header-title"},"选择国家/地区"),e.createElementVNode("text",{class:"header-subtitle"},"选择您的国家和地区"),e.createElementVNode("view",{class:"header-decoration"})]),e.createElementVNode("view",{class:"modern-content"},[e.createElementVNode("view",{class:"modern-card search-card fade-in-up"},[e.createElementVNode("view",{class:"card-header"},[e.createElementVNode("view",{class:"header-icon"},[e.createElementVNode("text",{class:"icon-text"},"🌍")]),e.createElementVNode("text",{class:"card-title"},"搜索国家")]),e.createElementVNode("view",{class:"search-form"},[e.withDirectives(e.createElementVNode("input",{class:"modern-input","onUpdate:modelValue":t[0]||(t[0]=e=>n.value=e),placeholder:"zh"===o.value?"用中文搜索国家":"Search countries in English",onInput:r},null,40,["placeholder"]),[[e.vModelText,n.value]]),e.createElementVNode("view",{class:"search-mode-toggle"},[e.createElementVNode("button",{class:e.normalizeClass(["mode-btn",{active:"zh"===o.value}]),onClick:t[1]||(t[1]=e=>c("zh"))}," 中文 ",2),e.createElementVNode("button",{class:e.normalizeClass(["mode-btn",{active:"en"===o.value}]),onClick:t[2]||(t[2]=e=>c("en"))}," English ",2)])])]),e.createElementVNode("view",{class:"modern-card regions-card fade-in-up"},[e.createElementVNode("view",{class:"card-header"},[e.createElementVNode("view",{class:"header-icon"},[e.createElementVNode("text",{class:"icon-text"},"📍")]),e.createElementVNode("text",{class:"card-title"},"国家/地区列表"),e.createElementVNode("text",{class:"card-subtitle"},e.toDisplayString(l.value.length)+"个结果",1)]),e.createElementVNode("scroll-view",{class:"regions-list","scroll-y":""},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(l.value,((a,t)=>(e.openBlock(),e.createElementBlock("view",{key:t,class:"region-item",onClick:e=>(e=>{const a={name:`${e.name}${e.flag}:${e.dialCode}`,code:e.dialCode};uni.navigateBack({delta:1,success:()=>{uni.$emit("updateRegion",a)}})})(a)},[e.createElementVNode("view",{class:"region-flag"},[e.createElementVNode("text",{class:"flag-text"},e.toDisplayString(a.flag),1)]),e.createElementVNode("view",{class:"region-info"},[e.createElementVNode("text",{class:"region-name"},e.toDisplayString(a.name),1),e.createElementVNode("text",{class:"region-details"},e.toDisplayString(a.enName)+" "+e.toDisplayString(a.dialCode),1)]),e.createElementVNode("text",{class:"select-arrow"},"›")],8,["onClick"])))),128))])])]),e.createElementVNode("view",{class:"bottom-decoration"})]))}},B={__name:"user",setup(a){const t=e.ref({avatar:"/common/images/1.png",nickname:"新用户",country:"中国🇨🇳",phone:"",email:""}),n=e.ref([{name:"中国🇨🇳",enName:"China",code:"CN"},{name:"阿布哈兹🇬🇪",enName:"Abkhazia",code:"GE"},{name:"阿富汗🇦🇫",enName:"Afghanistan",code:"AF"},{name:"阿尔巴尼亚🇦🇱",enName:"Albania",code:"AL"},{name:"阿尔及利亚🇩🇿",enName:"Algeria",code:"DZ"},{name:"安道尔🇦🇩",enName:"Andorra",code:"AD"},{name:"安哥拉🇦🇴",enName:"Angola",code:"AO"},{name:"安提瓜和巴布达🇦🇬",enName:"Antigua and Barbuda",code:"AG"},{name:"阿根廷🇦🇷",enName:"Argentina",code:"AR"},{name:"亚美尼亚🇦🇲",enName:"Armenia",code:"AM"},{name:"阿鲁巴🇦🇼",enName:"Aruba",code:"AW"},{name:"澳大利亚🇦🇺",enName:"Australia",code:"AU"},{name:"奥地利🇦🇹",enName:"Austria",code:"AT"},{name:"阿塞拜疆🇦🇿",enName:"Azerbaijan",code:"AZ"},{name:"巴哈马🇧🇸",enName:"Bahamas",code:"BS"},{name:"巴林🇧🇭",enName:"Bahrain",code:"BH"},{name:"孟加拉国🇧🇩",enName:"Bangladesh",code:"BD"},{name:"巴巴多斯🇧🇧",enName:"Barbados",code:"BB"},{name:"白俄罗斯🇧🇾",enName:"Belarus",code:"BY"},{name:"比利时🇧🇪",enName:"Belgium",code:"BE"},{name:"伯利兹🇧🇿",enName:"Belize",code:"BZ"},{name:"贝宁🇧🇯",enName:"Benin",code:"BJ"},{name:"百慕大🇧🇲",enName:"Bermuda",code:"BM"},{name:"不丹🇧🇹",enName:"Bhutan",code:"BT"},{name:"玻利维亚🇧🇴",enName:"Bolivia",code:"BO"},{name:"波斯尼亚和黑塞哥维那🇧🇦",enName:"Bosnia and Herzegovina",code:"BA"},{name:"博茨瓦纳🇧🇼",enName:"Botswana",code:"BW"},{name:"巴西🇧🇷",enName:"Brazil",code:"BR"},{name:"文莱🇧🇳",enName:"Brunei",code:"BN"},{name:"保加利亚🇧🇬",enName:"Bulgaria",code:"BG"},{name:"布基纳法索🇧🇫",enName:"Burkina Faso",code:"BF"},{name:"布隆迪🇧🇮",enName:"Burundi",code:"BI"},{name:"佛得角🇨🇻",enName:"Cabo Verde",code:"CV"},{name:"柬埔寨🇰🇭",enName:"Cambodia",code:"KH"},{name:"喀麦隆🇨🇲",enName:"Cameroon",code:"CM"},{name:"加拿大🇨🇦",enName:"Canada",code:"CA"},{name:"开曼群岛🇰🇾",enName:"Cayman Islands",code:"KY"},{name:"中非共和国🇨🇫",enName:"Central African Republic",code:"CF"},{name:"乍得🇹🇩",enName:"Chad",code:"TD"},{name:"智利🇨🇱",enName:"Chile",code:"CL"},{name:"哥伦比亚🇨🇴",enName:"Colombia",code:"CO"},{name:"科摩罗🇰🇲",enName:"Comoros",code:"KM"},{name:"刚果（布）🇨🇬",enName:"Congo (Brazzaville)",code:"CG"},{name:"刚果（金）🇨🇩",enName:"Congo (Kinshasa)",code:"CD"},{name:"库克群岛🇨🇰",enName:"Cook Islands",code:"CK"},{name:"哥斯达黎加🇨🇷",enName:"Costa Rica",code:"CR"},{name:"科特迪瓦🇨🇮",enName:"Côte d'Ivoire",code:"CI"},{name:"克罗地亚🇭🇷",enName:"Croatia",code:"HR"},{name:"古巴🇨🇺",enName:"Cuba",code:"CU"},{name:"库拉索🇨🇼",enName:"Curaçao",code:"CW"},{name:"塞浦路斯🇨🇾",enName:"Cyprus",code:"CY"},{name:"捷克共和国🇨🇿",enName:"Czech Republic",code:"CZ"},{name:"丹麦🇩🇰",enName:"Denmark",code:"DK"},{name:"吉布提🇩🇯",enName:"Djibouti",code:"DJ"},{name:"多米尼克🇩🇲",enName:"Dominica",code:"DM"},{name:"多米尼加共和国🇩🇴",enName:"Dominican Republic",code:"DO"},{name:"厄瓜多尔🇪🇨",enName:"Ecuador",code:"EC"},{name:"埃及🇪🇬",enName:"Egypt",code:"EG"},{name:"萨尔瓦多🇸🇻",enName:"El Salvador",code:"SV"},{name:"赤道几内亚🇬🇶",enName:"Equatorial Guinea",code:"GQ"},{name:"厄立特里亚🇪🇷",enName:"Eritrea",code:"ER"},{name:"爱沙尼亚🇪🇪",enName:"Estonia",code:"EE"},{name:"埃斯瓦蒂尼🇸🇿",enName:"Eswatini",code:"SZ"},{name:"埃塞俄比亚🇪🇹",enName:"Ethiopia",code:"ET"},{name:"斐济🇫🇯",enName:"Fiji",code:"FJ"},{name:"芬兰🇫🇮",enName:"Finland",code:"FI"},{name:"法国🇫🇷",enName:"France",code:"FR"},{name:"加蓬🇬🇦",enName:"Gabon",code:"GA"},{name:"冈比亚🇬🇲",enName:"Gambia",code:"GM"},{name:"格鲁吉亚🇬🇪",enName:"Georgia",code:"GE"},{name:"德国🇩🇪",enName:"Germany",code:"DE"},{name:"加纳🇬🇭",enName:"Ghana",code:"GH"},{name:"希腊🇬🇷",enName:"Greece",code:"GR"},{name:"格陵兰🇬🇱",enName:"Greenland",code:"GL"},{name:"格林纳达🇬🇽",enName:"Grenada",code:"GD"},{name:"关岛🇬🇺",enName:"Guam",code:"GU"},{name:"危地马拉🇬🇹",enName:"Guatemala",code:"GT"},{name:"几内亚🇬🇳",enName:"Guinea",code:"GN"},{name:"几内亚比绍🇬🇼",enName:"Guinea-Bissau",code:"GW"},{name:"圭亚那🇬🇾",enName:"Guyana",code:"GY"},{name:"海地🇭🇹",enName:"Haiti",code:"HT"},{name:"洪都拉斯🇭🇳",enName:"Honduras",code:"HN"},{name:"香港🇭🇰",enName:"Hong Kong",code:"HK"},{name:"匈牙利🇭🇺",enName:"Hungary",code:"HU"},{name:"冰岛🇮🇸",enName:"Iceland",code:"IS"},{name:"印度🇮🇳",enName:"India",code:"IN"},{name:"印度尼西亚🇮🇩",enName:"Indonesia",code:"ID"},{name:"伊朗🇮🇷",enName:"Iran",code:"IR"},{name:"伊拉克🇮🇶",enName:"Iraq",code:"IQ"},{name:"爱尔兰🇮🇪",enName:"Ireland",code:"IE"},{name:"以色列🇮🇱",enName:"Israel",code:"IL"},{name:"意大利🇮🇹",enName:"Italy",code:"IT"},{name:"牙买加🇯🇲",enName:"Jamaica",code:"JM"},{name:"日本🇯🇵",enName:"Japan",code:"JP"},{name:"约旦🇯🇴",enName:"Jordan",code:"JO"},{name:"哈萨克斯坦🇰🇿",enName:"Kazakhstan",code:"KZ"},{name:"肯尼亚🇰🇪",enName:"Kenya",code:"KE"},{name:"基里巴斯🇰🇮",enName:"Kiribati",code:"KI"},{name:"科索沃🇽🇰",enName:"Kosovo",code:"XK"},{name:"科威特🇰🇼",enName:"Kuwait",code:"KW"},{name:"吉尔吉斯斯坦🇰🇬",enName:"Kyrgyzstan",code:"KG"},{name:"老挝🇱🇦",enName:"Laos",code:"LA"},{name:"拉脱维亚🇱🇻",enName:"Latvia",code:"LV"},{name:"黎巴嫩🇱🇧",enName:"Lebanon",code:"LB"},{name:"莱索托🇱🇸",enName:"Lesotho",code:"LS"},{name:"利比里亚🇱🇷",enName:"Liberia",code:"LR"},{name:"利比亚🇱🇾",enName:"Libya",code:"LY"},{name:"列支敦士登🇱🇮",enName:"Liechtenstein",code:"LI"},{name:"立陶宛🇱🇹",enName:"Lithuania",code:"LT"},{name:"卢森堡🇱🇺",enName:"Luxembourg",code:"LU"},{name:"澳门🇲🇴",enName:"Macao",code:"MO"},{name:"马达加斯加🇲🇬",enName:"Madagascar",code:"MG"},{name:"马拉维🇲🇼",enName:"Malawi",code:"MW"},{name:"马来西亚🇲🇾",enName:"Malaysia",code:"MY"},{name:"马尔代夫🇲🇻",enName:"Maldives",code:"MV"},{name:"马里🇲🇱",enName:"Mali",code:"ML"},{name:"马耳他🇲🇹",enName:"Malta",code:"MT"},{name:"马绍尔群岛🇲🇭",enName:"Marshall Islands",code:"MH"},{name:"毛里塔尼亚🇲🇷",enName:"Mauritania",code:"MR"},{name:"毛里求斯🇲🇺",enName:"Mauritius",code:"MU"},{name:"墨西哥🇲🇽",enName:"Mexico",code:"MX"},{name:"密克罗尼西亚🇫🇲",enName:"Micronesia",code:"FM"},{name:"摩尔多瓦🇲🇩",enName:"Moldova",code:"MD"},{name:"摩纳哥🇲🇨",enName:"Monaco",code:"MC"},{name:"蒙古🇲🇳",enName:"Mongolia",code:"MN"},{name:"黑山🇲🇪",enName:"Montenegro",code:"ME"},{name:"摩洛哥🇲🇦",enName:"Morocco",code:"MA"},{name:"莫桑比克🇲🇿",enName:"Mozambique",code:"MZ"},{name:"缅甸🇲🇲",enName:"Myanmar",code:"MM"},{name:"纳米比亚🇳🇦",enName:"Namibia",code:"NA"},{name:"瑙鲁🇳🇷",enName:"Nauru",code:"NR"},{name:"尼泊尔🇳🇵",enName:"Nepal",code:"NP"},{name:"荷兰🇳🇱",enName:"Netherlands",code:"NL"},{name:"新西兰🇳🇿",enName:"New Zealand",code:"NZ"},{name:"尼加拉瓜🇳🇮",enName:"Nicaragua",code:"NI"},{name:"尼日尔🇳🇪",enName:"Niger",code:"NE"},{name:"尼日利亚🇳🇬",enName:"Nigeria",code:"NG"},{name:"纽埃🇳🇺",enName:"Niue",code:"NU"},{name:"北马其顿🇲🇰",enName:"North Macedonia",code:"MK"},{name:"北塞浦路斯🇹🇷",enName:"Northern Cyprus",code:"TR"},{name:"挪威🇳🇴",enName:"Norway",code:"NO"},{name:"阿曼🇴🇲",enName:"Oman",code:"OM"},{name:"巴基斯坦🇵🇰",enName:"Pakistan",code:"PK"},{name:"帕劳🇵🇼",enName:"Palau",code:"PW"},{name:"巴勒斯坦🇵🇸",enName:"Palestine",code:"PS"},{name:"巴拿马🇵🇦",enName:"Panama",code:"PA"},{name:"巴布亚新几内亚🇵🇬",enName:"Papua New Guinea",code:"PG"},{name:"巴拉圭🇵🇾",enName:"Paraguay",code:"PY"},{name:"秘鲁🇵🇪",enName:"Peru",code:"PE"},{name:"菲律宾🇵🇭",enName:"Philippines",code:"PH"},{name:"波兰🇵🇱",enName:"Poland",code:"PL"},{name:"葡萄牙🇵🇹",enName:"Portugal",code:"PT"},{name:"波多黎各🇵🇷",enName:"Puerto Rico",code:"PR"},{name:"卡塔尔🇶🇦",enName:"Qatar",code:"QA"},{name:"罗马尼亚🇷🇴",enName:"Romania",code:"RO"},{name:"俄罗斯🇷🇺",enName:"Russia",code:"RU"},{name:"卢旺达🇷🇼",enName:"Rwanda",code:"RW"},{name:"圣基茨和尼维斯🇰🇳",enName:"Saint Kitts and Nevis",code:"KN"},{name:"圣卢西亚🇱🇨",enName:"Saint Lucia",code:"LC"},{name:"圣文森特和格林纳丁斯🇻🇨",enName:"Saint Vincent and the Grenadines",code:"VC"},{name:"萨摩亚🇼🇸",enName:"Samoa",code:"WS"},{name:"圣马力诺🇸🇲",enName:"San Marino",code:"SM"},{name:"圣多美和普林西比🇸🇹",enName:"São Tomé and Príncipe",code:"ST"},{name:"沙特阿拉伯🇸🇦",enName:"Saudi Arabia",code:"SA"},{name:"塞内加尔🇸🇳",enName:"Senegal",code:"SN"},{name:"塞尔维亚🇷🇸",enName:"Serbia",code:"RS"},{name:"塞舌尔🇸🇨",enName:"Seychelles",code:"SC"},{name:"塞拉利昂🇸🇱",enName:"Sierra Leone",code:"SL"},{name:"新加坡🇸🇬",enName:"Singapore",code:"SG"},{name:"圣马丁（荷兰部分）🇸🇽",enName:"Sint Maarten",code:"SX"},{name:"斯洛伐克🇸🇰",enName:"Slovakia",code:"SK"},{name:"斯洛文尼亚🇸🇮",enName:"Slovenia",code:"SI"},{name:"所罗门群岛🇸🇧",enName:"Solomon Islands",code:"SB"},{name:"索马里🇸🇴",enName:"Somalia",code:"SO"},{name:"索马里兰🇸🇴",enName:"Somaliland",code:"SO"},{name:"南非🇿🇦",enName:"South Africa",code:"ZA"},{name:"南奥塞梯🇬🇪",enName:"South Ossetia",code:"GE"},{name:"南苏丹🇸🇸",enName:"South Sudan",code:"SS"},{name:"西班牙🇪🇸",enName:"Spain",code:"ES"},{name:"斯里兰卡🇱🇰",enName:"Sri Lanka",code:"LK"},{name:"苏丹🇸🇩",enName:"Sudan",code:"SD"},{name:"苏里南🇸🇷",enName:"Suriname",code:"SR"},{name:"瑞典🇸🇪",enName:"Sweden",code:"SE"},{name:"瑞士🇨🇭",enName:"Switzerland",code:"CH"},{name:"叙利亚🇸🇾",enName:"Syria",code:"SY"},{name:"台湾🇹🇼",enName:"Taiwan",code:"TW"},{name:"塔吉克斯坦🇹🇯",enName:"Tajikistan",code:"TJ"},{name:"坦桑尼亚🇹🇿",enName:"Tanzania",code:"TZ"},{name:"泰国🇹🇭",enName:"Thailand",code:"TH"},{name:"东帝汶🇹🇱",enName:"Timor-Leste",code:"TL"},{name:"多哥🇹🇬",enName:"Togo",code:"TG"},{name:"托克劳🇹🇰",enName:"Tokelau",code:"TK"},{name:"汤加🇹🇴",enName:"Tonga",code:"TO"},{name:"特立尼达和多巴哥🇹🇹",enName:"Trinidad and Tobago",code:"TT"},{name:"突尼斯🇹🇳",enName:"Tunisia",code:"TN"},{name:"土耳其🇹🇷",enName:"Turkey",code:"TR"},{name:"土库曼斯坦🇹🇲",enName:"Turkmenistan",code:"TM"},{name:"图瓦卢🇹🇻",enName:"Tuvalu",code:"TV"},{name:"乌干达🇺🇬",enName:"Uganda",code:"UG"},{name:"乌克兰🇺🇦",enName:"Ukraine",code:"UA"},{name:"阿联酋🇦🇪",enName:"United Arab Emirates",code:"AE"},{name:"英国🇬🇧",enName:"United Kingdom",code:"GB"},{name:"美国🇺🇸",enName:"United States",code:"US"},{name:"乌拉圭🇺🇾",enName:"Uruguay",code:"UY"},{name:"乌兹别克斯坦🇺🇿",enName:"Uzbekistan",code:"UZ"},{name:"瓦努阿图🇻🇺",enName:"Vanuatu",code:"VU"},{name:"梵蒂冈🇻🇦",enName:"Vatican City",code:"VA"},{name:"委内瑞拉🇻🇪",enName:"Venezuela",code:"VE"},{name:"越南🇻🇳",enName:"Vietnam",code:"VN"},{name:"也门🇾🇪",enName:"Yemen",code:"YE"},{name:"赞比亚🇿🇲",enName:"Zambia",code:"ZM"},{name:"津巴布韦🇿🇼",enName:"Zimbabwe",code:"ZW"}]),l=e.ref(0),o=e.ref(""),c=e.ref([]),r=e.ref("zh");e.onMounted((async()=>{await i();try{const e=await d({url:"/api/profile",method:"GET"});Object.assign(t.value,e)}catch(e){}}));const s=()=>{uni.chooseImage({count:1,sizeType:["original","compressed"],sourceType:["album","camera"],success:e=>{t.value.avatar=e.tempFilePaths[0]}})},m=e=>{l.value=e.detail.value,t.value.country=n.value[l.value].name,o.value="",c.value=n.value},u=e=>{r.value=e,o.value="",c.value=n.value},N=()=>{const e=o.value.trim().toLowerCase();c.value=e?n.value.filter((a=>"zh"===r.value?a.name.toLowerCase().includes(e):a.enName.toLowerCase().includes(e))):n.value},v=()=>{const e={avatar:t.value.avatar,username:t.value.username,country:t.value.country,phone:t.value.phone,email:t.value.email};uni.setStorageSync("userData",e),uni.showToast({title:"保存成功",icon:"success",success:()=>{setTimeout((()=>{uni.reLaunch({url:"/pages/enter/enter"})}),1e3)}})};return(a,d)=>(e.openBlock(),e.createElementBlock("view",{class:"modern-page"},[e.createElementVNode("view",{class:"modern-header"},[e.createElementVNode("text",{class:"header-title"},"个人资料"),e.createElementVNode("text",{class:"header-subtitle"},"管理个人信息，完善学习档案"),e.createElementVNode("view",{class:"header-decoration"})]),e.createElementVNode("view",{class:"modern-content"},[e.createElementVNode("view",{class:"modern-card profile-card fade-in-up"},[e.createElementVNode("view",{class:"card-header"},[e.createElementVNode("view",{class:"header-icon"},[e.createElementVNode("text",{class:"icon-text"},"👤")]),e.createElementVNode("text",{class:"card-title"},"个人信息")]),e.createElementVNode("view",{class:"avatar-section"},[e.createElementVNode("view",{class:"avatar-wrapper",onClick:s},[e.createElementVNode("image",{class:"avatar-image",src:t.value.avatar,mode:"aspectFill"},null,8,["src"]),e.createElementVNode("view",{class:"avatar-overlay"},[e.createElementVNode("text",{class:"overlay-text"},"📷")])]),e.createElementVNode("text",{class:"avatar-tip"},"点击更换头像")]),e.createElementVNode("view",{class:"form-section"},[e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"昵称"),e.withDirectives(e.createElementVNode("input",{class:"modern-input","onUpdate:modelValue":d[0]||(d[0]=e=>t.value.nickname=e),placeholder:"请输入昵称"},null,512),[[e.vModelText,t.value.nickname]])]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"国籍"),e.createElementVNode("view",{class:"country-selector"},[e.withDirectives(e.createElementVNode("input",{class:"modern-input search-input","onUpdate:modelValue":d[1]||(d[1]=e=>o.value=e),placeholder:"zh"===r.value?"用中文搜索国家":"Search countries in English",onInput:N},null,40,["placeholder"]),[[e.vModelText,o.value]]),e.createElementVNode("view",{class:"search-mode-toggle"},[e.createElementVNode("button",{class:e.normalizeClass(["mode-btn",{active:"zh"===r.value}]),onClick:d[2]||(d[2]=e=>u("zh"))}," 中文 ",2),e.createElementVNode("button",{class:e.normalizeClass(["mode-btn",{active:"en"===r.value}]),onClick:d[3]||(d[3]=e=>u("en"))}," English ",2)]),o.value&&c.value.length?(e.openBlock(),e.createElementBlock("scroll-view",{key:0,class:"search-results","scroll-y":""},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(c.value,((a,r)=>(e.openBlock(),e.createElementBlock("view",{key:r,class:"search-item",onClick:e=>(e=>{t.value.country=e.name;const a=n.value.findIndex((a=>a.name===e.name));l.value=-1!==a?a:0,o.value="",c.value=n.value})(a)},[e.createElementVNode("text",{class:"country-name"},e.toDisplayString(a.name),1)],8,["onClick"])))),128))])):e.createCommentVNode("",!0),o.value&&!c.value.length?(e.openBlock(),e.createElementBlock("view",{key:1,class:"no-results"},[e.createElementVNode("text",{class:"no-results-text"},"无匹配国家")])):e.createCommentVNode("",!0),e.createElementVNode("picker",{onChange:m,value:l.value,range:n.value,"range-key":"name",class:"country-picker"},[e.createElementVNode("view",{class:"picker-display"},[e.createElementVNode("text",{class:"selected-country"},e.toDisplayString(n.value[l.value].name),1),e.createElementVNode("text",{class:"picker-arrow"},"▼")])],40,["value","range"])])]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"手机号"),e.withDirectives(e.createElementVNode("input",{class:"modern-input","onUpdate:modelValue":d[4]||(d[4]=e=>t.value.phone=e),placeholder:"请输入手机号",type:"number"},null,512),[[e.vModelText,t.value.phone]])]),e.createElementVNode("view",{class:"form-item"},[e.createElementVNode("text",{class:"form-label"},"邮箱"),e.withDirectives(e.createElementVNode("input",{class:"modern-input","onUpdate:modelValue":d[5]||(d[5]=e=>t.value.email=e),placeholder:"请输入邮箱",type:"text"},null,512),[[e.vModelText,t.value.email]])])]),e.createElementVNode("button",{class:"modern-button primary save-btn",onClick:v},[e.createElementVNode("text",{class:"btn-icon"},"💾"),e.createElementVNode("text",{class:"btn-text"},"保存资料")])])]),e.createElementVNode("view",{class:"bottom-decoration"})]))}};__definePage("pages/index/index",{__name:"index",setup(a){const t=()=>{const e=(new Date).getHours();return e<12?"早上好":e<18?"下午好":"晚上好"},n=e.ref(t());return e.onMounted((()=>{n.value=t()})),(a,t)=>(e.openBlock(),e.createElementBlock("view",{class:"index-page"},[e.createElementVNode("view",{class:"header-section"},[e.createElementVNode("view",{class:"welcome-text"},[e.createElementVNode("text",{class:"greeting"},e.toDisplayString(n.value),1),e.createElementVNode("text",{class:"subtitle"},"开始您的高效学习之旅")]),e.createElementVNode("view",{class:"header-decoration"})]),e.createElementVNode("view",{class:"banner-container"},[e.createElementVNode("swiper",{circular:"true","indicator-dots":"true","indicator-color":"rgba(255,255,255,0.3)","indicator-active-color":"#6366f1",autoplay:"true",class:"modern-swiper"},[e.createElementVNode("swiper-item",null,[e.createElementVNode("view",{class:"banner-item"},[e.createElementVNode("image",{src:"/assets/1.bcb9d2bf.png",mode:"aspectFill",class:"banner-image"}),e.createElementVNode("view",{class:"banner-overlay"},[e.createElementVNode("text",{class:"banner-title"},"智能学习助手"),e.createElementVNode("text",{class:"banner-desc"},"AI驱动的个性化学习体验")])])]),e.createElementVNode("swiper-item",null,[e.createElementVNode("view",{class:"banner-item"},[e.createElementVNode("image",{src:"/assets/2.c8a4c7ba.png",mode:"aspectFill",class:"banner-image"}),e.createElementVNode("view",{class:"banner-overlay"},[e.createElementVNode("text",{class:"banner-title"},"高效时间管理"),e.createElementVNode("text",{class:"banner-desc"},"科学规划，提升学习效率")])])]),e.createElementVNode("swiper-item",null,[e.createElementVNode("view",{class:"banner-item"},[e.createElementVNode("image",{src:"/assets/3.302e224c.png",mode:"aspectFill",class:"banner-image"}),e.createElementVNode("view",{class:"banner-overlay"},[e.createElementVNode("text",{class:"banner-title"},"成就激励系统"),e.createElementVNode("text",{class:"banner-desc"},"记录每一次进步与成长")])])])])]),e.createElementVNode("view",{class:"features-section"},[e.createElementVNode("view",{class:"section-header"},[e.createElementVNode("text",{class:"section-title"},"核心功能"),e.createElementVNode("text",{class:"section-subtitle"},"探索强大的学习工具")]),e.createElementVNode("view",{class:"features-grid"},[e.createElementVNode("navigator",{url:"/pages/plan/plan",class:"feature-card-nav"},[e.createElementVNode("view",{class:"feature-card plan-card"},[e.createElementVNode("view",{class:"card-icon"},[e.createElementVNode("text",{class:"icon-text"},"📋")]),e.createElementVNode("view",{class:"card-content"},[e.createElementVNode("text",{class:"card-title"},"智能计划"),e.createElementVNode("text",{class:"card-desc"},"AI辅助制定学习计划")]),e.createElementVNode("view",{class:"card-arrow"},[e.createElementVNode("text",{class:"arrow-icon"},"→")])])]),e.createElementVNode("navigator",{url:"/pages/daily/daily",class:"feature-card-nav"},[e.createElementVNode("view",{class:"feature-card checkin-card"},[e.createElementVNode("view",{class:"card-icon"},[e.createElementVNode("text",{class:"icon-text"},"✓")]),e.createElementVNode("view",{class:"card-content"},[e.createElementVNode("text",{class:"card-title"},"每日打卡"),e.createElementVNode("text",{class:"card-desc"},"养成良好学习习惯")]),e.createElementVNode("view",{class:"card-arrow"},[e.createElementVNode("text",{class:"arrow-icon"},"→")])])]),e.createElementVNode("navigator",{url:"/pages/monitor/monitor",class:"feature-card-nav"},[e.createElementVNode("view",{class:"feature-card monitor-card"},[e.createElementVNode("view",{class:"card-icon"},[e.createElementVNode("text",{class:"icon-text"},"📊")]),e.createElementVNode("view",{class:"card-content"},[e.createElementVNode("text",{class:"card-title"},"学习监测"),e.createElementVNode("text",{class:"card-desc"},"实时专注度分析")]),e.createElementVNode("view",{class:"card-arrow"},[e.createElementVNode("text",{class:"arrow-icon"},"→")])])]),e.createElementVNode("navigator",{url:"/pages/achievement/achievement",class:"feature-card-nav"},[e.createElementVNode("view",{class:"feature-card achievement-card"},[e.createElementVNode("view",{class:"card-icon"},[e.createElementVNode("text",{class:"icon-text"},"🏆")]),e.createElementVNode("view",{class:"card-content"},[e.createElementVNode("text",{class:"card-title"},"成就系统"),e.createElementVNode("text",{class:"card-desc"},"记录学习里程碑")]),e.createElementVNode("view",{class:"card-arrow"},[e.createElementVNode("text",{class:"arrow-icon"},"→")])])])])]),e.createElementVNode("view",{class:"bottom-decoration"})]))}}),__definePage("pages/society/society",a),__definePage("pages/achievement/achievement",t),__definePage("pages/enter/enter",n),__definePage("pages/plan/plan",l),__definePage("pages/daily/daily",o),__definePage("pages/monitor/monitor",s),__definePage("pages/planmanagement/planmanagement",m),__definePage("pages/timereminder/timereminder",u),__definePage("pages/dailypunch/dailypunch",N),__definePage("pages/schedule/schedule",v),__definePage("pages/dataanalysis/dataanalysis",E),__definePage("pages/evaluate/evaluate",g),__definePage("pages/achievementwall/achievementwall",V),__definePage("pages/saying/saying",p),__definePage("pages/rankinglist/rankinglist",w),__definePage("pages/friends/friends",h),__definePage("pages/social/social",f),__definePage("pages/profile/profile",x),__definePage("pages/studyrecords/studyrecords",y),__definePage("pages/setting/setting",k),__definePage("pages/helpfeedback/helpfeedback",C),__definePage("pages/login/login",S),__definePage("pages/nation/nation",b),__definePage("pages/user/user",B);const D={onLaunch:function(){c("log","at App.vue:4","App Launch")},onShow:function(){c("log","at App.vue:7","App Show")},onHide:function(){c("log","at App.vue:10","App Hide")}};const{app:T,Vuex:M,Pinia:L}={app:e.createVueApp(D)};uni.Vuex=M,uni.Pinia=L,T.provide("__globalStyles",__uniConfig.styles),T._component.mpType="app",T._component.render=()=>{},T.mount("#app")}(Vue);
