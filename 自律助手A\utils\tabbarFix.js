/**
 * TabBar修复工具
 * 专门解决安卓设备上TabBar显示空白的问题
 */

class TabBarFix {
  constructor() {
    this.isAndroid = false
    this.systemInfo = null
    this.init()
  }

  /**
   * 初始化
   */
  init() {
    try {
      this.systemInfo = uni.getSystemInfoSync()
      this.isAndroid = this.systemInfo.platform === 'android'

      if (this.isAndroid) {
        console.log('TabBarFix: 检测到安卓设备，启用TabBar修复')
        this.applyAndroidFix()
      }
    } catch (error) {
      console.error('TabBarFix: 初始化失败', error)
    }
  }

  /**
   * 应用安卓修复
   */
  applyAndroidFix() {
    // 延迟执行，确保DOM已加载
    setTimeout(() => {
      this.injectFixStyles()
    }, 100)
  }

  /**
   * 注入修复样式
   */
  injectFixStyles() {
    // 创建样式元素
    const styleId = 'tabbar-android-fix'
    if (document.getElementById(styleId)) {
      return // 已经注入过了
    }

    const style = document.createElement('style')
    style.id = styleId
    style.innerHTML = `
      /* 安卓TabBar修复样式 */
      .uni-tabbar {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        position: fixed !important;
        bottom: 0 !important;
        left: 0 !important;
        right: 0 !important;
        width: 100% !important;
        height: 50px !important;
        background-color: #ffffff !important;
        border-top: 1px solid #e5e5e5 !important;
        box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1) !important;
        z-index: 1000 !important;
        transform: translateZ(0) !important;
        -webkit-transform: translateZ(0) !important;
        will-change: transform !important;
      }

      .uni-tabbar .uni-tabbar__item {
        flex: 1 !important;
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        justify-content: center !important;
        height: 50px !important;
        padding: 4px 0 !important;
        position: relative !important;
        background: transparent !important;
        border: none !important;
        outline: none !important;
        -webkit-tap-highlight-color: transparent !important;
      }

      .uni-tabbar .uni-tabbar__icon {
        width: 24px !important;
        height: 24px !important;
        margin-bottom: 2px !important;
        display: block !important;
        background-size: contain !important;
        background-repeat: no-repeat !important;
        background-position: center !important;
      }

      .uni-tabbar .uni-tabbar__icon img {
        width: 100% !important;
        height: 100% !important;
        display: block !important;
      }

      .uni-tabbar .uni-tabbar__text {
        font-size: 10px !important;
        line-height: 1 !important;
        color: #8a8a8a !important;
        text-align: center !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        max-width: 100% !important;
      }

      .uni-tabbar .uni-tabbar__item.uni-tabbar__item--selected .uni-tabbar__text {
        color: #667eea !important;
      }

      /* 触摸反馈 */
      .uni-tabbar .uni-tabbar__item:active {
        background-color: rgba(102, 126, 234, 0.1) !important;
        transform: scale(0.95) !important;
        transition: all 0.1s ease !important;
      }

      /* 页面底部间距 */
      page {
        padding-bottom: 50px !important;
      }

      .uni-page-wrapper {
        padding-bottom: 50px !important;
      }
    `

    document.head.appendChild(style)
    console.log('TabBarFix: 安卓修复样式注入完成')
  }

  /**
   * 检查TabBar状态
   */
  checkTabBarStatus() {
    return new Promise((resolve) => {
      const query = uni.createSelectorQuery()
      query.select('.uni-tabbar').boundingClientRect((data) => {
        const isVisible = !!(data && data.height > 0)
        resolve({
          exists: !!data,
          visible: isVisible,
          height: data ? data.height : 0,
          width: data ? data.width : 0
        })
      }).exec()
    })
  }

  /**
   * 手动触发修复
   */
  manualFix() {
    if (this.isAndroid) {
      this.injectFixStyles()
      console.log('TabBarFix: 手动修复完成')
    }
  }
}

// 创建全局实例
const tabbarFix = new TabBarFix()

// 导出工具函数
export default {
  // 检查TabBar状态
  checkStatus: () => tabbarFix.checkTabBarStatus(),

  // 手动修复
  fix: () => tabbarFix.manualFix(),

  // 获取系统信息
  getSystemInfo: () => tabbarFix.systemInfo,

  // 是否为安卓设备
  isAndroid: () => tabbarFix.isAndroid
}
