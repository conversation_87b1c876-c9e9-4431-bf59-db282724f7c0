
  ;(function(){
  let u=void 0,isReady=false,onReadyCallbacks=[],isServiceReady=false,onServiceReadyCallbacks=[];
  const __uniConfig = {"pages":[],"globalStyle":{"backgroundColor":"linear-gradient(135deg, #667eea 0%, #764ba2 100%)","enablePullDownRefresh":true,"backgroundColorTop":"#667eea","backgroundColorBottom":"#764ba2","navigationBar":{"backgroundColor":"#667eea","titleText":"自律助手","style":"default","type":"default","titleColor":"#ffffff"},"isNVue":false},"nvue":{"compiler":"uni-app","styleCompiler":"uni-app","flex-direction":"column"},"renderer":"auto","appname":"自律助手","splashscreen":{"alwaysShowBeforeRender":true,"autoclose":true},"compilerVersion":"4.56","entryPagePath":"pages/index/index","entryPageQuery":"","realEntryPagePath":"","networkTimeout":{"request":60000,"connectSocket":60000,"uploadFile":60000,"downloadFile":60000},"tabBar":{"position":"bottom","color":"#8a8a8a","selectedColor":"#667eea","borderStyle":"white","blurEffect":"none","fontSize":"10px","iconWidth":"24px","spacing":"3px","height":"50px","list":[{"pagePath":"pages/index/index","iconPath":"/common/images/tabbar/首页未选.png","selectedIconPath":"/common/images/tabbar/首页.png"},{"pagePath":"pages/society/society","iconPath":"/common/images/tabbar/社区未选.png","selectedIconPath":"/common/images/tabbar/社区.png"},{"pagePath":"pages/enter/enter","iconPath":"/common/images/tabbar/个人中心未选.png","selectedIconPath":"/common/images/tabbar/个人中心.png"}],"backgroundColor":"#ffffff","selectedIndex":0,"shown":true},"locales":{},"darkmode":false,"themeConfig":{}};
  const __uniRoutes = [{"path":"pages/index/index","meta":{"isQuit":true,"isEntry":true,"isTabBar":true,"tabBarIndex":0,"enablePullDownRefresh":false,"navigationBar":{"titleText":" ","type":"default"},"isNVue":false}},{"path":"pages/society/society","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":1,"enablePullDownRefresh":false,"navigationBar":{"titleText":" ","type":"default"},"isNVue":false}},{"path":"pages/achievement/achievement","meta":{"enablePullDownRefresh":false,"navigationBar":{"titleText":" ","type":"default"},"isNVue":false}},{"path":"pages/enter/enter","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":2,"enablePullDownRefresh":false,"navigationBar":{"titleText":" ","type":"default"},"isNVue":false}},{"path":"pages/plan/plan","meta":{"enablePullDownRefresh":false,"navigationBar":{"titleText":" ","type":"default"},"isNVue":false}},{"path":"pages/daily/daily","meta":{"enablePullDownRefresh":false,"navigationBar":{"titleText":" ","type":"default"},"isNVue":false}},{"path":"pages/monitor/monitor","meta":{"enablePullDownRefresh":false,"navigationBar":{"titleText":" ","type":"default"},"isNVue":false}},{"path":"pages/planmanagement/planmanagement","meta":{"enablePullDownRefresh":false,"navigationBar":{"titleText":" ","type":"default"},"isNVue":false}},{"path":"pages/timereminder/timereminder","meta":{"enablePullDownRefresh":false,"navigationBar":{"titleText":" ","type":"default"},"isNVue":false}},{"path":"pages/dailypunch/dailypunch","meta":{"enablePullDownRefresh":false,"navigationBar":{"titleText":" ","type":"default"},"isNVue":false}},{"path":"pages/schedule/schedule","meta":{"enablePullDownRefresh":false,"navigationBar":{"titleText":" ","type":"default"},"isNVue":false}},{"path":"pages/dataanalysis/dataanalysis","meta":{"enablePullDownRefresh":false,"navigationBar":{"titleText":" ","type":"default"},"isNVue":false}},{"path":"pages/evaluate/evaluate","meta":{"enablePullDownRefresh":false,"navigationBar":{"titleText":" ","type":"default"},"isNVue":false}},{"path":"pages/achievementwall/achievementwall","meta":{"enablePullDownRefresh":false,"navigationBar":{"titleText":" ","type":"default"},"isNVue":false}},{"path":"pages/saying/saying","meta":{"enablePullDownRefresh":false,"navigationBar":{"titleText":" ","type":"default"},"isNVue":false}},{"path":"pages/rankinglist/rankinglist","meta":{"enablePullDownRefresh":false,"navigationBar":{"titleText":" ","type":"default"},"isNVue":false}},{"path":"pages/friends/friends","meta":{"enablePullDownRefresh":false,"navigationBar":{"titleText":" ","type":"default"},"isNVue":false}},{"path":"pages/social/social","meta":{"enablePullDownRefresh":false,"navigationBar":{"titleText":" ","type":"default"},"isNVue":false}},{"path":"pages/profile/profile","meta":{"enablePullDownRefresh":false,"navigationBar":{"titleText":" ","type":"default"},"isNVue":false}},{"path":"pages/studyrecords/studyrecords","meta":{"enablePullDownRefresh":false,"navigationBar":{"titleText":" ","type":"default"},"isNVue":false}},{"path":"pages/setting/setting","meta":{"enablePullDownRefresh":false,"navigationBar":{"titleText":" ","type":"default"},"isNVue":false}},{"path":"pages/helpfeedback/helpfeedback","meta":{"enablePullDownRefresh":false,"navigationBar":{"titleText":" ","type":"default"},"isNVue":false}},{"path":"pages/login/login","meta":{"enablePullDownRefresh":false,"navigationBar":{"titleText":" ","type":"default"},"isNVue":false}},{"path":"pages/nation/nation","meta":{"enablePullDownRefresh":false,"navigationBar":{"titleText":" ","type":"default"},"isNVue":false}},{"path":"pages/user/user","meta":{"enablePullDownRefresh":false,"navigationBar":{"titleText":" ","type":"default"},"isNVue":false}}].map(uniRoute=>(uniRoute.meta.route=uniRoute.path,__uniConfig.pages.push(uniRoute.path),uniRoute.path='/'+uniRoute.path,uniRoute));
  __uniConfig.styles=[];//styles
  __uniConfig.onReady=function(callback){if(__uniConfig.ready){callback()}else{onReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"ready",{get:function(){return isReady},set:function(val){isReady=val;if(!isReady){return}const callbacks=onReadyCallbacks.slice(0);onReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
  __uniConfig.onServiceReady=function(callback){if(__uniConfig.serviceReady){callback()}else{onServiceReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"serviceReady",{get:function(){return isServiceReady},set:function(val){isServiceReady=val;if(!isServiceReady){return}const callbacks=onServiceReadyCallbacks.slice(0);onServiceReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
  service.register("uni-app-config",{create(a,b,c){if(!__uniConfig.viewport){var d=b.weex.config.env.scale,e=b.weex.config.env.deviceWidth,f=Math.ceil(e/d);Object.assign(__uniConfig,{viewport:f,defaultFontSize:16})}return{instance:{__uniConfig:__uniConfig,__uniRoutes:__uniRoutes,global:u,window:u,document:u,frames:u,self:u,location:u,navigator:u,localStorage:u,history:u,Caches:u,screen:u,alert:u,confirm:u,prompt:u,fetch:u,XMLHttpRequest:u,WebSocket:u,webkit:u,print:u}}}}); 
  })();
  