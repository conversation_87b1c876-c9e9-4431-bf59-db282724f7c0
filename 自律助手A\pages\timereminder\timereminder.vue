<template>
  <view class="modern-page">
    <!-- 头部区域 -->
    <view class="modern-header">
      <text class="header-title">时间提醒</text>
      <text class="header-subtitle">智能提醒，不错过任何重要时刻</text>
      <view class="header-decoration"></view>
    </view>

    <!-- 内容区域 -->
    <view class="modern-content">
      <!-- 添加提醒卡片 -->
      <view class="modern-card add-reminder-card fade-in-up" @click="showAddModal = true">
        <view class="add-icon">
          <text class="icon-text">⏰</text>
        </view>
        <text class="add-title">添加新提醒</text>
        <text class="add-desc">设置重要事项提醒</text>
      </view>

      <!-- 提醒列表 -->
      <view class="reminders-section" v-if="reminders.length > 0">
        <view class="section-header">
          <text class="section-title">我的提醒</text>
          <text class="section-subtitle">{{ reminders.length }}个提醒</text>
        </view>

        <view class="reminder-list">
          <view class="modern-card reminder-item fade-in-up" v-for="(reminder, index) in reminders" :key="index">
            <view class="reminder-header">
              <view class="reminder-icon">
                <text class="icon-text">🔔</text>
              </view>
              <view class="reminder-info">
                <text class="reminder-title">{{ reminder.title }}</text>
                <text class="reminder-time">{{ reminder.time }}</text>
              </view>
            </view>

            <view class="reminder-actions">
              <button class="modern-button warning action-btn" @click="editReminder(index)">
                编辑
              </button>
              <button class="modern-button danger action-btn" @click="deleteReminder(index)">
                删除
              </button>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="no-reminders" v-if="reminders.length === 0">
        <view class="empty-icon">
          <text class="icon-text">⏰</text>
        </view>
        <text class="empty-text">暂无提醒</text>
        <text class="empty-desc">点击上方按钮创建您的第一个提醒</text>
      </view>
    </view>

    <!-- 添加/编辑提醒模态框 -->
    <view class="modern-modal" v-if="showAddModal">
      <view class="modal-overlay" @click="closeModal"></view>
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">{{ isEditing ? '编辑提醒' : '添加提醒' }}</text>
          <view class="close-button" @click="closeModal">
            <text class="close-icon">×</text>
          </view>
        </view>

        <view class="modal-body">
          <view class="form-group">
            <text class="form-label">提醒内容</text>
            <input
              type="text"
              v-model="newReminder.title"
              class="modern-input"
              placeholder="请输入提醒内容"
            />
          </view>

          <view class="form-group">
            <text class="form-label">选择日期</text>
            <picker mode="date" :value="newReminder.date" @change="onDateChange">
              <view class="modern-input picker-input">
                <text class="picker-text">{{ newReminder.date || '选择日期' }}</text>
                <text class="picker-icon">📅</text>
              </view>
            </picker>
          </view>

          <view class="form-group">
            <text class="form-label">选择时间</text>
            <picker mode="time" :value="newReminder.time" @change="onTimeChange">
              <view class="modern-input picker-input">
                <text class="picker-text">{{ newReminder.time || '选择时间' }}</text>
                <text class="picker-icon">⏰</text>
              </view>
            </picker>
          </view>
        </view>

        <view class="modal-footer">
          <button class="modern-button cancel-btn" @click="closeModal">
            取消
          </button>
          <button class="modern-button primary submit-btn" @click="saveReminder">
            {{ isEditing ? '保存' : '添加' }}
          </button>
        </view>
      </view>
    </view>

    <!-- 底部装饰 -->
    <view class="bottom-decoration"></view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';

const showAddModal = ref(false);
const isEditing = ref(false);
const editingIndex = ref(-1);

import { apiRequest } from '@/utils/request.js';
const reminders = ref([]);
const newReminder = reactive({
  title: '',
  date: '',
  time: '',
  repeat: '',
});

// 加载提醒数据（后端API）
const loadReminders = async () => {
  try {
    const res = await apiRequest({ url: '/api/reminders', method: 'GET' });
    reminders.value = res || [];
  } catch (e) {
    reminders.value = [];
  }
};

// 设置提醒通知
const scheduleNotification = (reminder) => {
  const [date, time] = reminder.time.split(' ');
  const [year, month, day] = date.split('-').map(Number);
  const [hour, minute] = time.split(':').map(Number);

  const reminderTime = new Date(year, month - 1, day, hour, minute).getTime();
  const now = Date.now();

  if (reminderTime > now) {
    const delay = reminderTime - now;
    setTimeout(() => {
      uni.showToast({
        title: `提醒: ${reminder.title}`,
        icon: 'none',
        duration: 5000,
      });
    }, delay);
  }
};

// 日期选择
const onDateChange = (e) => {
  newReminder.date = e.detail.value;
};

// 时间选择
const onTimeChange = (e) => {
  newReminder.time = e.detail.value;
};

// 添加或保存提醒
const saveReminder = () => {
  if (!newReminder.title || !newReminder.date || !newReminder.time) {
    uni.showToast({ title: '请填写完整信息', icon: 'none' });
    return;
  }
  const fullTime = `${newReminder.date} ${newReminder.time}`;
  if (isEditing.value) {
    reminders.value[editingIndex.value] = { title: newReminder.title, time: fullTime };
  } else {
    reminders.value.push({ title: newReminder.title, time: fullTime });
  }
  scheduleNotification({ title: newReminder.title, time: fullTime }); // 设置通知
  saveReminders();
  closeModal();
};

// 编辑提醒
const editReminder = (index) => {
  isEditing.value = true;
  editingIndex.value = index;
  const [date, time] = reminders.value[index].time.split(' ');
  newReminder.title = reminders.value[index].title;
  newReminder.date = date;
  newReminder.time = time;
  showAddModal.value = true;
};

// 删除提醒
const deleteReminder = (index) => {
  reminders.value.splice(index, 1);
  saveReminders();
};

// 关闭模态框并重置
const closeModal = () => {
  showAddModal.value = false;
  isEditing.value = false;
  editingIndex.value = -1;
  newReminder.title = '';
  newReminder.date = '';
  newReminder.time = '';
};

// 组件挂载时加载数据并检查提醒
const checkReminders = () => {
  loadReminders();
  reminders.value.forEach((reminder) => {
    scheduleNotification(reminder);
  });
};

onMounted(() => {
  checkReminders();
});
</script>

<style lang="scss">
// 使用全局现代化主题样式
.bottom-decoration {
  height: 100rpx;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.1), transparent);
  position: relative;
  z-index: 1;
}

// 添加提醒卡片样式
.add-reminder-card {
  text-align: center;
  padding: 40rpx 32rpx;
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    transform: translateY(2rpx);
  }

  .add-icon {
    width: 80rpx;
    height: 80rpx;
    margin: 0 auto 20rpx;
    background: var(--monitor-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    .icon-text {
      font-size: 40rpx;
      color: white;
    }
  }

  .add-title {
    display: block;
    font-size: 28rpx;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 8rpx;
  }

  .add-desc {
    display: block;
    font-size: 22rpx;
    color: var(--gray-500);
  }
}

// 提醒列表区域样式
.reminders-section {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;

    .section-title {
      font-size: 28rpx;
      font-weight: 600;
      color: var(--white);
    }

    .section-subtitle {
      font-size: 22rpx;
      color: rgba(255, 255, 255, 0.8);
    }
  }
}

.reminder-list {
  .reminder-item {
    padding: 32rpx 24rpx;
    margin-bottom: 20rpx;

    .reminder-header {
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;

      .reminder-icon {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20rpx;
        background: var(--monitor-gradient);

        .icon-text {
          font-size: 24rpx;
          color: white;
        }
      }

      .reminder-info {
        flex: 1;

        .reminder-title {
          font-size: 28rpx;
          font-weight: 600;
          color: var(--gray-800);
          margin-bottom: 6rpx;
          display: block;
        }

        .reminder-time {
          font-size: 22rpx;
          color: var(--gray-500);
          display: block;
        }
      }
    }

    .reminder-actions {
      display: flex;
      gap: 16rpx;

      .action-btn {
        flex: 1;
        height: 60rpx;
        font-size: 24rpx;
      }
    }
  }
}

.no-reminders {
  text-align: center;
  padding: 80rpx 20rpx;

  .empty-icon {
    width: 120rpx;
    height: 120rpx;
    margin: 0 auto 24rpx;
    background: var(--gray-200);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    .icon-text {
      font-size: 60rpx;
      color: var(--gray-400);
    }
  }

  .empty-text {
    display: block;
    font-size: 28rpx;
    font-weight: 500;
    color: var(--gray-600);
    margin-bottom: 8rpx;
  }

  .empty-desc {
    display: block;
    font-size: 22rpx;
    color: var(--gray-400);
  }
}

// 现代化模态框样式
.modern-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;

  .modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4rpx);
  }

  .modal-content {
    width: 85%;
    max-width: 600rpx;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20rpx);
    border-radius: var(--radius-2xl);
    overflow: hidden;
    position: relative;
    z-index: 1;
    box-shadow: var(--shadow-2xl);
    animation: modalSlideIn 0.3s ease-out;
  }

  .modal-header {
    padding: 32rpx 32rpx 24rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1rpx solid var(--gray-200);

    .modal-title {
      font-size: 32rpx;
      font-weight: 600;
      color: var(--gray-800);
    }

    .close-button {
      width: 48rpx;
      height: 48rpx;
      border-radius: 50%;
      background: var(--gray-100);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      &:active {
        background: var(--gray-200);
        transform: scale(0.95);
      }

      .close-icon {
        font-size: 32rpx;
        color: var(--gray-500);
        font-weight: 300;
      }
    }
  }

  .modal-body {
    padding: 32rpx;
  }

  .form-group {
    margin-bottom: 32rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .form-label {
      display: block;
      font-size: 26rpx;
      font-weight: 500;
      color: var(--gray-700);
      margin-bottom: 12rpx;
    }

    .modern-input {
      width: 100%;
      height: 88rpx;
      border: 2rpx solid var(--gray-200);
      border-radius: var(--radius-lg);
      padding: 0 24rpx;
      font-size: 28rpx;
      color: var(--gray-800);
      background: var(--white);
      transition: all 0.3s ease;

      &:focus {
        border-color: var(--monitor-gradient);
        box-shadow: 0 0 0 4rpx rgba(245, 158, 11, 0.1);
      }

      &::placeholder {
        color: var(--gray-400);
      }
    }

    .picker-input {
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;

      .picker-text {
        color: var(--gray-800);
        font-size: 28rpx;
      }

      .picker-icon {
        font-size: 24rpx;
        color: var(--gray-400);
      }
    }
  }

  .modal-footer {
    padding: 24rpx 32rpx 32rpx;
    border-top: 1rpx solid var(--gray-200);
    display: flex;
    gap: 16rpx;

    .cancel-btn {
      flex: 1;
      height: 80rpx;
      background: var(--gray-100);
      color: var(--gray-600);
      border: 1rpx solid var(--gray-200);
    }

    .submit-btn {
      flex: 2;
      height: 80rpx;
    }
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50rpx) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
</style>