# AI学习监测功能说明

## 功能概述

本功能集成了Google Gemini AI图像识别技术，可以通过分析用户的学习状态照片来评估专注度和疲劳指数，替代原有的随机数据生成方式。

## 主要特性

### 1. AI图像识别
- 使用免费的Gemini 1.5 Flash模型
- 分析用户面部表情、坐姿、眼神等
- 实时评估专注度（0-100分）和疲劳指数（0-100分）
- 提供识别置信度和状态描述

### 2. 智能降级
- 当AI服务不可用时自动切换到随机模式
- 用户可手动开关AI识别功能
- 保证功能的稳定性和可用性

### 3. 开箱即用
- 内置API密钥，无需用户配置
- 即开即用的AI识别功能
- 简化的用户操作流程

## 使用步骤

### 第一步：使用AI监测

1. 点击"开始监测"启动监测
2. 确保AI识别开关处于开启状态
3. 点击"拍照分析"按钮进行状态分析
4. 查看分析结果和置信度
5. 根据需要重复拍照分析

## 技术实现

### 文件结构
```
utils/
├── geminiService.js    # Gemini API服务封装
├── config.js          # 配置管理模块
pages/monitor/
└── monitor.vue        # 主要监测页面
```

### 核心模块

#### 1. geminiService.js
- 封装Gemini API调用逻辑
- 处理图像base64转换
- 解析AI返回结果
- 错误处理和降级方案

#### 2. config.js
- API密钥安全存储
- 用户偏好设置管理
- 配置对话框封装

#### 3. monitor.vue
- 摄像头预览和拍照
- AI识别状态管理
- 用户界面交互
- 数据可视化

### AI分析流程

1. **图像捕获**：使用uni-app的camera组件拍照
2. **格式转换**：将图像转换为base64格式
3. **API调用**：发送到Gemini API进行分析
4. **结果解析**：解析JSON格式的分析结果
5. **数据更新**：更新专注度和疲劳指数
6. **状态显示**：显示置信度和描述信息

## 注意事项

### 安全性
- API密钥内置在应用中，安全可靠
- 图像仅用于实时分析，不会保存或上传
- 所有数据处理均在客户端完成

### 性能优化
- 使用Gemini 1.5 Flash模型，响应速度快
- 图像压缩为normal质量，平衡效果和速度
- 30秒超时设置，避免长时间等待

### 兼容性
- 支持前置摄像头拍照
- 兼容uni-app多端部署
- 降级方案确保功能可用性

## 故障排除

### 常见问题

1. **摄像头无法启动**
   - 检查设备权限设置
   - 确认摄像头硬件正常
   - 重启应用重试

2. **API调用失败**
   - 检查网络连接
   - 确认网络可访问Google服务
   - 查看控制台错误信息

3. **识别结果不准确**
   - 确保光线充足
   - 保持面部清晰可见
   - 避免遮挡和模糊

### 调试信息
- 所有错误信息会输出到控制台
- 网络请求状态可在开发者工具中查看
- 识别结果包含置信度用于判断准确性

## 更新日志

### v1.0.0
- 集成Gemini AI图像识别
- 添加API密钥配置功能
- 实现智能降级机制
- 优化用户界面和交互体验

## 技术支持

如遇到问题，请检查：
1. 网络连接是否正常
2. 设备权限是否授予
3. 控制台是否有错误信息
4. 摄像头是否正常工作

更多技术细节请参考源代码注释。
