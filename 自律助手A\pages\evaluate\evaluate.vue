<template>
  <view class="modern-page">
    <!-- 头部区域 -->
    <view class="modern-header">
      <text class="header-title">学习评价</text>
      <text class="header-subtitle">全面评估学习表现，持续改进提升</text>
      <view class="header-decoration"></view>
    </view>

    <!-- 内容区域 -->
    <view class="modern-content">
      <!-- 总体评价卡片 -->
      <view class="modern-card overall-card fade-in-up">
        <view class="card-header">
          <view class="header-icon">
            <text class="icon-text">⭐</text>
          </view>
          <text class="card-title">总体评价</text>
        </view>

        <view class="overall-content">
          <view class="score-circle">
            <text class="score-value">{{ overallScore }}</text>
            <text class="score-unit">分</text>
          </view>
          <text class="evaluation-text">{{ overallComment }}</text>
        </view>
      </view>

      <!-- 详细评价卡片 -->
      <view class="modern-card detailed-card fade-in-up">
        <view class="card-header">
          <view class="header-icon">
            <text class="icon-text">📊</text>
          </view>
          <text class="card-title">详细评价</text>
        </view>

        <view class="evaluation-list">
          <view class="evaluation-item" v-for="(item, index) in evaluations" :key="index">
            <view class="item-header">
              <view class="item-icon" :class="`item-${index}`">
                <text class="icon-text">{{ getItemIcon(item.label) }}</text>
              </view>
              <view class="item-info">
                <text class="item-label">{{ item.label }}</text>
                <text class="item-score">{{ item.label === '学习时长' ? `${item.score}分钟` : `${item.score}%` }}</text>
              </view>
            </view>
            <text class="item-comment">{{ item.comment }}</text>
          </view>
        </view>
      </view>

      <!-- 反馈提交卡片 -->
      <view class="modern-card feedback-card fade-in-up">
        <view class="card-header">
          <view class="header-icon">
            <text class="icon-text">💬</text>
          </view>
          <text class="card-title">提交反馈</text>
        </view>

        <view class="feedback-content">
          <textarea
            class="modern-textarea"
            v-model="feedback"
            placeholder="请输入您的反馈意见..."
          ></textarea>
          <button class="modern-button primary submit-button" @click="submitFeedback">
            提交反馈
          </button>
        </view>
      </view>
    </view>

    <!-- 底部装饰 -->
    <view class="bottom-decoration"></view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 总体评价
const overallScore = ref(0);
const overallComment = ref('');

// 详细评价
const evaluations = ref([]);

import { apiRequest } from '@/utils/request.js';
const feedback = ref('');

// 加载数据
const loadData = () => {
  try {
    // 加载 Monitor.vue 数据
    const storedMonitorRecords = uni.getStorageSync('monitorRecords') || '[]';
    const monitorRecords = JSON.parse(storedMonitorRecords);
    const monitorData = calculateMonitorData(monitorRecords);

    // 加载 Dailypunch.vue 数据
    const storedPunchRecords = uni.getStorageSync('punchRecords') || '[]';
    const punchRecords = JSON.parse(storedPunchRecords);
    const punchData = calculatePunchData(punchRecords);

    // 设置详细评价
    evaluations.value = [
      { label: '专注度', score: monitorData.avgFocus, comment: generateFocusComment(monitorData.avgFocus) },
      { label: '完成率', score: punchData.completionRate, comment: generateCompletionComment(punchData.completionRate) },
      { label: '学习时长', score: monitorData.totalStudyTime, comment: generateStudyTimeComment(monitorData.totalStudyTime) },
    ];

    // 计算总体评价（专注度和完成率的平均值）
    overallScore.value = Math.round((monitorData.avgFocus + punchData.completionRate) / 2);
    overallComment.value = generateOverallComment(overallScore.value);

    // 加载反馈
    const storedFeedbacks = uni.getStorageSync('feedbacks') || '[]';
    feedbacks.value = JSON.parse(storedFeedbacks);
  } catch (e) {
    console.error('加载数据失败', e);
    evaluations.value = [
      { label: '专注度', score: 0, comment: '暂无数据' },
      { label: '完成率', score: 0, comment: '暂无数据' },
      { label: '学习时长', score: 0, comment: '暂无数据' },
    ];
    overallScore.value = 0;
    overallComment.value = '暂无评价数据';
    feedbacks.value = [];
  }
};

// 计算 Monitor.vue 数据
const calculateMonitorData = (records) => {
  const focusLevels = [];
  let totalStudyTime = 0;

  records.forEach(record => {
    const match = record.detail.match(/平均专注度 (\d+)%/);
    if (match) {
      focusLevels.push(parseInt(match[1]));
    }
  });

  // 计算总监测时长（分钟）
  totalStudyTime = records.reduce((sum, record) => {
    const timeMatch = record.detail.match(/监测时长: (\d{2}):(\d{2}):(\d{2})/);
    if (timeMatch) {
      const hours = parseInt(timeMatch[1]);
      const minutes = parseInt(timeMatch[2]);
      const seconds = parseInt(timeMatch[3]);
      return sum + (hours * 60) + minutes + (seconds / 60); // 转换为分钟
    }
    return sum;
  }, 0);

  const avgFocus = focusLevels.length > 0 ? Math.round(focusLevels.reduce((sum, val) => sum + val, 0) / focusLevels.length) : 0;

  return { avgFocus, totalStudyTime: Math.round(totalStudyTime) };
};

// 计算 Dailypunch.vue 数据
const calculatePunchData = (records) => {
  const totalPunchDays = records.filter(record => record.punched).length;
  const startDate = new Date('2025-01-01'); // 假设起始日期
  const today = new Date();
  const totalDays = Math.ceil((today - startDate) / (1000 * 60 * 60 * 24));
  const completionRate = totalDays > 0 ? Math.round((totalPunchDays / totalDays) * 100) : 0;

  return { completionRate };
};

// 生成评价评论
const generateFocusComment = (score) => {
  if (score >= 70) return '专注时间较长，效率高。';
  if (score >= 50) return '专注度一般，需提升稳定性。';
  return '专注度较低，建议优化学习环境。';
};

const generateCompletionComment = (score) => {
  if (score >= 70) return '大部分任务按时完成。';
  if (score >= 50) return '完成率中等，需加强坚持。';
  return '完成率较低，建议调整计划。';
};

const generateStudyTimeComment = (minutes) => {
  if (minutes >= 600) return '学习投入非常充分。'; // 10小时
  if (minutes >= 300) return '学习投入稳定。'; // 5小时
  return '学习时长较短，建议增加投入。';
};

const generateOverallComment = (score) => {
  if (score >= 70) return '您的学习表现优秀，继续保持！';
  if (score >= 50) return '学习表现良好，稍作调整更佳。';
  return '学习表现有待提升，建议优化学习策略。';
};

// 获取项目图标
const getItemIcon = (label) => {
  switch (label) {
    case '专注度': return '🎯';
    case '完成率': return '✅';
    case '学习时长': return '⏰';
    default: return '📊';
  }
};

// 提交反馈
const submitFeedback = async () => {
  if (!feedback.value.trim()) {
    uni.showToast({ title: '反馈内容不能为空', icon: 'none' });
    return;
  }
  try {
    await apiRequest({ url: '/api/feedback', method: 'POST', data: { content: feedback.value } });
    uni.showToast({ title: '反馈提交成功', icon: 'success' });
    feedback.value = '';
  } catch (e) {
    uni.showToast({ title: e?.error || '提交失败', icon: 'none' });
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadData();
});
</script>

<style lang="scss">
// 使用全局现代化主题样式
.bottom-decoration {
  height: 100rpx;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.1), transparent);
  position: relative;
  z-index: 1;
}

// 总体评价卡片样式
.overall-card {
  .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 32rpx;

    .header-icon {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      background: var(--achievement-gradient);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16rpx;

      .icon-text {
        font-size: 24rpx;
        color: white;
      }
    }

    .card-title {
      font-size: 28rpx;
      font-weight: 600;
      color: var(--gray-800);
    }
  }

  .overall-content {
    text-align: center;

    .score-circle {
      width: 160rpx;
      height: 160rpx;
      border-radius: 50%;
      background: var(--achievement-gradient);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin: 0 auto 24rpx;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        inset: 12rpx;
        border-radius: 50%;
        background: white;
      }

      .score-value {
        font-size: 48rpx;
        font-weight: 700;
        color: var(--gray-800);
        position: relative;
        z-index: 1;
        line-height: 1;
      }

      .score-unit {
        font-size: 20rpx;
        color: var(--gray-500);
        position: relative;
        z-index: 1;
        margin-top: 4rpx;
      }
    }

    .evaluation-text {
      font-size: 26rpx;
      color: var(--gray-600);
      line-height: 1.5;
      max-width: 400rpx;
      margin: 0 auto;
    }
  }
}

// 详细评价卡片样式
.detailed-card {
  .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;

    .header-icon {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      background: var(--plan-gradient);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16rpx;

      .icon-text {
        font-size: 24rpx;
        color: white;
      }
    }

    .card-title {
      font-size: 28rpx;
      font-weight: 600;
      color: var(--gray-800);
    }
  }

  .evaluation-list {
    .evaluation-item {
      padding: 24rpx 0;
      border-bottom: 1rpx solid var(--gray-200);

      &:last-child {
        border-bottom: none;
      }

      .item-header {
        display: flex;
        align-items: center;
        margin-bottom: 12rpx;

        .item-icon {
          width: 50rpx;
          height: 50rpx;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16rpx;

          &.item-0 { background: var(--monitor-gradient); }
          &.item-1 { background: var(--checkin-gradient); }
          &.item-2 { background: var(--achievement-gradient); }

          .icon-text {
            font-size: 20rpx;
            color: white;
          }
        }

        .item-info {
          flex: 1;

          .item-label {
            font-size: 26rpx;
            font-weight: 600;
            color: var(--gray-800);
            margin-bottom: 4rpx;
            display: block;
          }

          .item-score {
            font-size: 24rpx;
            color: var(--gray-600);
            font-weight: 500;
            display: block;
          }
        }
      }

      .item-comment {
        font-size: 22rpx;
        color: var(--gray-500);
        line-height: 1.4;
        padding-left: 66rpx;
      }
    }
  }
}

// 反馈卡片样式
.feedback-card {
  .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;

    .header-icon {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      background: var(--social-gradient);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16rpx;

      .icon-text {
        font-size: 24rpx;
        color: white;
      }
    }

    .card-title {
      font-size: 28rpx;
      font-weight: 600;
      color: var(--gray-800);
    }
  }

  .feedback-content {
    .modern-textarea {
      width: 100%;
      height: 200rpx;
      border: 2rpx solid var(--gray-200);
      border-radius: var(--radius-lg);
      padding: 20rpx;
      font-size: 26rpx;
      color: var(--gray-800);
      background: var(--white);
      margin-bottom: 24rpx;
      resize: none;
      transition: all 0.3s ease;

      &:focus {
        border-color: var(--social-gradient);
        box-shadow: 0 0 0 4rpx rgba(6, 182, 212, 0.1);
      }

      &::placeholder {
        color: var(--gray-400);
      }
    }

    .submit-button {
      width: 100%;
      height: 80rpx;
    }
  }
}
</style>