.bottom-decoration{height:3.125rem;background:linear-gradient(to top,rgba(255,255,255,.1),transparent);position:relative;z-index:1}.date-card{text-align:center;padding:1.25rem 1rem}.date-card .date-icon{width:2.5rem;height:2.5rem;margin:0 auto .625rem;background:var(--plan-gradient);border-radius:50%;display:flex;align-items:center;justify-content:center}.date-card .date-icon .icon-text{font-size:1.25rem;color:#fff}.date-card .current-date{display:block;font-size:1.125rem;font-weight:600;color:var(--gray-800);margin-bottom:.25rem}.date-card .date-label{display:block;font-size:.75rem;color:var(--gray-500)}.punch-status-card{text-align:center;padding:1.25rem 1rem}.punch-status-card .status-icon{width:3.75rem;height:3.75rem;margin:0 auto .75rem;background:var(--gray-200);border-radius:50%;display:flex;align-items:center;justify-content:center;transition:all .3s ease}.punch-status-card .status-icon.punched{background:var(--checkin-gradient)}.punch-status-card .status-icon .icon-text{font-size:1.875rem;color:var(--gray-600)}.punch-status-card .status-icon.punched .icon-text{color:#fff}.punch-status-card .status-text{display:block;font-size:1rem;font-weight:600;color:var(--gray-800);margin-bottom:.375rem}.punch-status-card .status-text.punched{color:var(--checkin-gradient)}.punch-status-card .status-desc{display:block;font-size:.75rem;color:var(--gray-500);margin-bottom:1rem;line-height:1.4}.punch-status-card .punch-button{width:7.5rem;height:2.5rem}.punch-status-card .punch-button.disabled{background:var(--gray-300)!important;color:var(--gray-500)!important;cursor:not-allowed}.history-card .section-header{display:flex;justify-content:space-between;align-items:center;margin-bottom:.75rem}.history-card .section-header .section-title{font-size:.875rem;font-weight:600;color:var(--gray-800)}.history-card .section-header .section-subtitle{font-size:.6875rem;color:var(--gray-500)}.history-card .history-list .history-item{display:flex;justify-content:space-between;align-items:center;padding:.625rem 0;border-bottom:.03125rem solid var(--gray-200)}.history-card .history-list .history-item:last-child{border-bottom:none}.history-card .history-list .history-item .history-date{font-size:.8125rem;color:var(--gray-600);font-weight:500}.history-card .history-list .history-item .history-status{font-size:.75rem;font-weight:500}.history-card .history-list .history-item .history-status.punched{color:var(--checkin-gradient)}.history-card .history-list .no-records{text-align:center;padding:1.875rem .625rem}.history-card .history-list .no-records .empty-icon{width:3.125rem;height:3.125rem;margin:0 auto .625rem;background:var(--gray-200);border-radius:50%;display:flex;align-items:center;justify-content:center}.history-card .history-list .no-records .empty-icon .icon-text{font-size:1.5625rem;color:var(--gray-400)}.history-card .history-list .no-records .empty-text{display:block;font-size:.875rem;font-weight:500;color:var(--gray-600);margin-bottom:.25rem}.history-card .history-list .no-records .empty-desc{display:block;font-size:.6875rem;color:var(--gray-400)}
