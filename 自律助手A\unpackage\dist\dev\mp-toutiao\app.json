{"pages": ["pages/index/index", "pages/society/society", "pages/achievement/achievement", "pages/enter/enter", "pages/plan/plan", "pages/daily/daily"], "window": {"navigationBarTextStyle": "black", "navigationBarTitleText": "uni-app", "navigationBarBackgroundColor": "#FFF5EE", "backgroundColor": "#F8F8F8", "enablePullDownRefresh": true}, "tabBar": {"list": [{"pagePath": "pages/index/index", "text": "首页"}, {"pagePath": "pages/society/society", "text": "社区"}, {"pagePath": "pages/achievement/achievement", "text": "成就"}, {"pagePath": "pages/enter/enter", "text": "登录"}]}, "usingComponents": {}}