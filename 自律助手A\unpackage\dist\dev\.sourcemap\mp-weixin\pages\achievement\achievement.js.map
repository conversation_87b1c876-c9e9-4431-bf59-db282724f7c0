{"version": 3, "file": "achievement.js", "sources": ["../../../../../../HBuilderX.4.29.2024093009/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYWNoaWV2ZW1lbnQvYWNoaWV2ZW1lbnQudnVl"], "sourcesContent": ["import MiniProgramPage from 'D:/.Resources/大创/自律助手vue/自律助手/自律助手/自律助手/pages/achievement/achievement.vue'\nwx.createPage(MiniProgramPage)"], "names": ["MiniProgramPage"], "mappings": ";;;;;;;;;AACA,GAAG,WAAWA,SAAe;"}