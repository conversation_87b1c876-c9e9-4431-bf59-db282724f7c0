<template>
  <view class="modern-page">
    <!-- 头部区域 -->
    <view class="modern-header">
      <text class="header-title">每日打卡</text>
      <text class="header-subtitle">坚持每一天，养成好习惯</text>
      <view class="header-decoration"></view>
    </view>

    <!-- 内容区域 -->
    <view class="modern-content">
      <!-- 当前日期卡片 -->
      <view class="modern-card date-card fade-in-up">
        <view class="date-icon">
          <text class="icon-text">📅</text>
        </view>
        <text class="current-date">{{ currentDate }}</text>
        <text class="date-label">今日日期</text>
      </view>

      <!-- 打卡状态卡片 -->
      <view class="modern-card punch-status-card fade-in-up">
        <view class="status-icon" :class="{ 'punched': isPunched }">
          <text class="icon-text">{{ isPunched ? '✅' : '⏰' }}</text>
        </view>
        <text class="status-text" :class="{ 'punched': isPunched }">
          {{ isPunched ? '今日已打卡' : '今日未打卡' }}
        </text>
        <text class="status-desc">
          {{ isPunched ? '恭喜您完成今日打卡！' : '点击下方按钮完成打卡' }}
        </text>
        <button
          class="modern-button punch-button"
          :class="{ 'success': !isPunched, 'disabled': isPunched }"
          :disabled="isPunched"
          @click="handlePunch"
        >
          {{ isPunched ? '已完成' : '立即打卡' }}
        </button>
      </view>

      <!-- 打卡历史卡片 -->
      <view class="modern-card history-card fade-in-up">
        <view class="section-header">
          <text class="section-title">打卡历史</text>
          <text class="section-subtitle">{{ punchRecords.length }}条记录</text>
        </view>
        <view class="history-list">
          <view class="history-item" v-for="(record, index) in punchRecords" :key="index">
            <view class="history-date-section">
              <text class="history-date">{{ record.date }}</text>
            </view>
            <view class="history-status-section">
              <text class="history-status" :class="{ 'punched': record.punched }">
                {{ record.punched ? '✅ 已打卡' : '❌ 未打卡' }}
              </text>
            </view>
          </view>
          <view class="no-records" v-if="punchRecords.length === 0">
            <view class="empty-icon">
              <text class="icon-text">📝</text>
            </view>
            <text class="empty-text">暂无打卡记录</text>
            <text class="empty-desc">开始您的第一次打卡吧！</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部装饰 -->
    <view class="bottom-decoration"></view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { apiRequest } from '@/utils/request.js';

// 当前日期
const currentDate = computed(() => {
  const date = new Date();
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
});

// 打卡状态
const isPunched = ref(false);

// 打卡记录
const punchRecords = ref([]);

// 首次升级时迁移本地旧数据到后端
const migrateLocalPunchRecords = async () => {
  try {
    const storedRecords = uni.getStorageSync('punchRecords');
    if (storedRecords) {
      const records = JSON.parse(storedRecords);
      // 批量上传到后端
      for (const rec of records) {
        if (rec.date && rec.punched) {
          await apiRequest({ url: '/api/punches', method: 'POST', data: { date: rec.date, content: '', type: '每日打卡', created_at: rec.date } });
        }
      }
      uni.removeStorageSync('punchRecords'); // 清除本地数据
    }
  } catch (e) {
    console.error('本地打卡数据迁移失败', e);
  }
};

// 加载打卡历史
const loadPunchRecords = async () => {
  try {
    const res = await apiRequest({ url: '/api/punches', method: 'GET' });
    punchRecords.value = (res || []).map(r => ({ date: r.date, punched: true }));
    // 检查今日是否已打卡
    isPunched.value = punchRecords.value.some(record => record.date === currentDate.value && record.punched);
  } catch (e) {
    punchRecords.value = [];
    isPunched.value = false;
  }
};

// 处理打卡
const handlePunch = async () => {
  try {
    await apiRequest({ url: '/api/punches', method: 'POST', data: { date: currentDate.value, content: '', type: '每日打卡', created_at: currentDate.value } });
    uni.showToast({ title: '打卡成功', icon: 'success' });
    await loadPunchRecords();
  } catch (e) {
    // 错误已全局提示
  }
};

onMounted(async () => {
  await migrateLocalPunchRecords();
  await loadPunchRecords();
});
</script>

<style lang="scss">
// 使用全局现代化主题样式
.bottom-decoration {
  height: 100rpx;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.1), transparent);
  position: relative;
  z-index: 1;
}

// 日期卡片样式
.date-card {
  text-align: center;
  padding: 40rpx 32rpx;

  .date-icon {
    width: 80rpx;
    height: 80rpx;
    margin: 0 auto 20rpx;
    background: var(--plan-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    .icon-text {
      font-size: 40rpx;
      color: white;
    }
  }

  .current-date {
    display: block;
    font-size: 36rpx;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 8rpx;
  }

  .date-label {
    display: block;
    font-size: 24rpx;
    color: var(--gray-500);
  }
}

// 打卡状态卡片样式
.punch-status-card {
  text-align: center;
  padding: 40rpx 32rpx;

  .status-icon {
    width: 120rpx;
    height: 120rpx;
    margin: 0 auto 24rpx;
    background: var(--gray-200);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    &.punched {
      background: var(--checkin-gradient);
    }

    .icon-text {
      font-size: 60rpx;
      color: var(--gray-600);
    }

    &.punched .icon-text {
      color: white;
    }
  }

  .status-text {
    display: block;
    font-size: 32rpx;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 12rpx;

    &.punched {
      color: var(--checkin-gradient);
    }
  }

  .status-desc {
    display: block;
    font-size: 24rpx;
    color: var(--gray-500);
    margin-bottom: 32rpx;
    line-height: 1.4;
  }

  .punch-button {
    width: 240rpx;
    height: 80rpx;

    &.disabled {
      background: var(--gray-300) !important;
      color: var(--gray-500) !important;
      cursor: not-allowed;
    }
  }
}

// 历史记录卡片样式
.history-card {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;

    .section-title {
      font-size: 28rpx;
      font-weight: 600;
      color: var(--gray-800);
    }

    .section-subtitle {
      font-size: 22rpx;
      color: var(--gray-500);
    }
  }

  .history-list {
    .history-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx 0;
      border-bottom: 1rpx solid var(--gray-200);

      &:last-child {
        border-bottom: none;
      }

      .history-date {
        font-size: 26rpx;
        color: var(--gray-600);
        font-weight: 500;
      }

      .history-status {
        font-size: 24rpx;
        font-weight: 500;

        &.punched {
          color: var(--checkin-gradient);
        }
      }
    }

    .no-records {
      text-align: center;
      padding: 60rpx 20rpx;

      .empty-icon {
        width: 100rpx;
        height: 100rpx;
        margin: 0 auto 20rpx;
        background: var(--gray-200);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        .icon-text {
          font-size: 50rpx;
          color: var(--gray-400);
        }
      }

      .empty-text {
        display: block;
        font-size: 28rpx;
        font-weight: 500;
        color: var(--gray-600);
        margin-bottom: 8rpx;
      }

      .empty-desc {
        display: block;
        font-size: 22rpx;
        color: var(--gray-400);
      }
    }
  }
}
</style>