<template>
  <view class="modern-page">
    <!-- 头部区域 -->
    <view class="modern-header">
      <text class="header-title">计划管理</text>
      <text class="header-subtitle">创建和管理您的学习计划</text>
      <view class="header-decoration"></view>
    </view>

    <!-- 内容区域 -->
    <view class="modern-content">
      <!-- 添加计划卡片 -->
      <view class="modern-card add-plan-card fade-in-up" @click="showAddModal = true">
        <view class="add-icon">
          <text class="icon-text">➕</text>
        </view>
        <text class="add-title">添加新计划</text>
        <text class="add-desc">制定新的学习目标</text>
      </view>

      <!-- 计划列表 -->
      <view class="plans-section">
        <view class="section-header" v-if="plans.length > 0">
          <text class="section-title">我的计划</text>
          <text class="section-subtitle">{{ plans.length }}个计划</text>
        </view>

        <view class="plan-list">
          <view class="modern-card plan-item fade-in-up" v-for="(plan, index) in plans" :key="index">
            <view class="plan-header">
              <view class="plan-status-icon" :class="{ 'completed': plan.completed }">
                <text class="icon-text">{{ plan.completed ? '✅' : '⏰' }}</text>
              </view>
              <view class="plan-info">
                <text class="plan-title">{{ plan.title }}</text>
                <text class="plan-time">{{ plan.time }}</text>
              </view>
            </view>

            <view class="plan-status-badge" :class="{ 'completed': plan.completed }">
              <text class="status-text">{{ plan.completed ? '已完成' : '进行中' }}</text>
            </view>

            <view class="plan-actions">
              <button
                class="modern-button action-btn"
                :class="{ 'success': !plan.completed, 'warning': plan.completed }"
                @click="togglePlanStatus(index)"
              >
                {{ plan.completed ? '标记未完成' : '标记完成' }}
              </button>
              <button class="modern-button danger action-btn" @click="deletePlan(index)">
                删除
              </button>
            </view>
          </view>

          <view class="no-plans" v-if="plans.length === 0">
            <view class="empty-icon">
              <text class="icon-text">📝</text>
            </view>
            <text class="empty-text">暂无计划</text>
            <text class="empty-desc">点击上方按钮创建您的第一个计划</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 添加计划模态框 -->
    <view class="modern-modal" v-if="showAddModal">
      <view class="modal-overlay" @click="showAddModal = false"></view>
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">添加新计划</text>
          <view class="close-button" @click="showAddModal = false">
            <text class="close-icon">×</text>
          </view>
        </view>

        <view class="modal-body">
          <view class="form-group">
            <text class="form-label">计划名称</text>
            <input
              type="text"
              v-model="newPlan.title"
              class="modern-input"
              placeholder="请输入计划名称"
            />
          </view>

          <view class="form-group">
            <text class="form-label">选择日期</text>
            <picker mode="date" :value="newPlan.date" @change="onDateChange">
              <view class="modern-input picker-input">
                <text class="picker-text">{{ newPlan.date || '选择日期' }}</text>
                <text class="picker-icon">📅</text>
              </view>
            </picker>
          </view>

          <view class="form-group">
            <text class="form-label">选择时间</text>
            <picker mode="time" :value="newPlan.time" @change="onTimeChange">
              <view class="modern-input picker-input">
                <text class="picker-text">{{ newPlan.time || '选择时间' }}</text>
                <text class="picker-icon">⏰</text>
              </view>
            </picker>
          </view>
        </view>

        <view class="modal-footer">
          <button class="modern-button cancel-btn" @click="showAddModal = false">
            取消
          </button>
          <button class="modern-button primary submit-btn" @click="addPlan">
            添加计划
          </button>
        </view>
      </view>
    </view>

    <!-- 底部装饰 -->
    <view class="bottom-decoration"></view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { apiRequest } from '@/utils/request.js';
import { migrateAllLocalData } from '@/utils/migrateLocalData.js';

const plans = ref([]);
const showAddModal = ref(false);
const newPlan = reactive({
  title: '',
  date: '',
  time: '',
});

// 加载计划数据（后端）
const loadPlans = async () => {
  try {
    const res = await apiRequest({ url: '/api/plans', method: 'GET' });
    plans.value = (res || []).map(item => ({
      id: item.id,
      title: item.title,
      time: item.remind_time,
      completed: !!item.completed
    }));
  } catch (e) {
    plans.value = [];
  }
};

// 日期选择
const onDateChange = (e) => {
  newPlan.date = e.detail.value;
};

// 时间选择
const onTimeChange = (e) => {
  newPlan.time = e.detail.value;
};

// 添加计划（后端）
const addPlan = async () => {
  if (!newPlan.title || !newPlan.date || !newPlan.time) {
    uni.showToast({ title: '请填写完整信息', icon: 'none' });
    return;
  }
  const fullTime = `${newPlan.date} ${newPlan.time}`;
  try {
    await apiRequest({ url: '/api/plans', method: 'POST', data: { title: newPlan.title, remind_time: fullTime, completed: false, created_at: fullTime } });
    uni.showToast({ title: '添加成功', icon: 'success' });
    showAddModal.value = false;
    newPlan.title = '';
    newPlan.date = '';
    newPlan.time = '';
    await loadPlans();
  } catch (e) {}
};

// 切换计划状态（后端）
const togglePlanStatus = async (index) => {
  const plan = plans.value[index];
  if (!plan) return;
  try {
    await apiRequest({ url: `/api/plans/${plan.id}`, method: 'PUT', data: { completed: !plan.completed } });
    await loadPlans();
  } catch (e) {}
};

// 删除计划（后端）
const deletePlan = async (index) => {
  const plan = plans.value[index];
  if (!plan) return;
  try {
    await apiRequest({ url: `/api/plans/${plan.id}`, method: 'DELETE' });
    await loadPlans();
  } catch (e) {}
};

onMounted(async () => {
  await migrateAllLocalData(); // 首次升级自动迁移本地所有数据到后端
  await loadPlans();
});


onMounted(() => {
  loadPlans();
});
</script>

<style lang="scss">
// 使用全局现代化主题样式
.bottom-decoration {
  height: 100rpx;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.1), transparent);
  position: relative;
  z-index: 1;
}

// 添加计划卡片样式
.add-plan-card {
  text-align: center;
  padding: 40rpx 32rpx;
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    transform: translateY(2rpx);
  }

  .add-icon {
    width: 80rpx;
    height: 80rpx;
    margin: 0 auto 20rpx;
    background: var(--plan-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    .icon-text {
      font-size: 40rpx;
      color: white;
    }
  }

  .add-title {
    display: block;
    font-size: 28rpx;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 8rpx;
  }

  .add-desc {
    display: block;
    font-size: 22rpx;
    color: var(--gray-500);
  }
}

// 计划列表区域样式
.plans-section {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;

    .section-title {
      font-size: 28rpx;
      font-weight: 600;
      color: var(--white);
    }

    .section-subtitle {
      font-size: 22rpx;
      color: rgba(255, 255, 255, 0.8);
    }
  }
}

.plan-list {
  .plan-item {
    padding: 32rpx 24rpx;
    margin-bottom: 20rpx;

    .plan-header {
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;

      .plan-status-icon {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20rpx;
        background: var(--gray-200);
        transition: all 0.3s ease;

        &.completed {
          background: var(--checkin-gradient);
        }

        .icon-text {
          font-size: 24rpx;
          color: var(--gray-600);
        }

        &.completed .icon-text {
          color: white;
        }
      }

      .plan-info {
        flex: 1;

        .plan-title {
          font-size: 28rpx;
          font-weight: 600;
          color: var(--gray-800);
          margin-bottom: 6rpx;
          display: block;
        }

        .plan-time {
          font-size: 22rpx;
          color: var(--gray-500);
          display: block;
        }
      }
    }

    .plan-status-badge {
      display: inline-block;
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
      margin-bottom: 20rpx;
      background: var(--gray-200);

      &.completed {
        background: var(--checkin-gradient);
      }

      .status-text {
        font-size: 20rpx;
        font-weight: 500;
        color: var(--gray-600);
      }

      &.completed .status-text {
        color: white;
      }
    }

    .plan-actions {
      display: flex;
      gap: 16rpx;

      .action-btn {
        flex: 1;
        height: 60rpx;
        font-size: 24rpx;
      }
    }
  }

  .no-plans {
    text-align: center;
    padding: 80rpx 20rpx;

    .empty-icon {
      width: 120rpx;
      height: 120rpx;
      margin: 0 auto 24rpx;
      background: var(--gray-200);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      .icon-text {
        font-size: 60rpx;
        color: var(--gray-400);
      }
    }

    .empty-text {
      display: block;
      font-size: 28rpx;
      font-weight: 500;
      color: var(--gray-600);
      margin-bottom: 8rpx;
    }

    .empty-desc {
      display: block;
      font-size: 22rpx;
      color: var(--gray-400);
    }
  }
}

// 现代化模态框样式
.modern-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;

  .modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4rpx);
  }

  .modal-content {
    width: 85%;
    max-width: 600rpx;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20rpx);
    border-radius: var(--radius-2xl);
    overflow: hidden;
    position: relative;
    z-index: 1;
    box-shadow: var(--shadow-2xl);
    animation: modalSlideIn 0.3s ease-out;
  }

  .modal-header {
    padding: 32rpx 32rpx 24rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1rpx solid var(--gray-200);

    .modal-title {
      font-size: 32rpx;
      font-weight: 600;
      color: var(--gray-800);
    }

    .close-button {
      width: 48rpx;
      height: 48rpx;
      border-radius: 50%;
      background: var(--gray-100);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      &:active {
        background: var(--gray-200);
        transform: scale(0.95);
      }

      .close-icon {
        font-size: 32rpx;
        color: var(--gray-500);
        font-weight: 300;
      }
    }
  }

  .modal-body {
    padding: 32rpx;
  }

  .form-group {
    margin-bottom: 32rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .form-label {
      display: block;
      font-size: 26rpx;
      font-weight: 500;
      color: var(--gray-700);
      margin-bottom: 12rpx;
    }

    .modern-input {
      width: 100%;
      height: 88rpx;
      border: 2rpx solid var(--gray-200);
      border-radius: var(--radius-lg);
      padding: 0 24rpx;
      font-size: 28rpx;
      color: var(--gray-800);
      background: var(--white);
      transition: all 0.3s ease;

      &:focus {
        border-color: var(--plan-gradient);
        box-shadow: 0 0 0 4rpx rgba(99, 102, 241, 0.1);
      }

      &::placeholder {
        color: var(--gray-400);
      }
    }

    .picker-input {
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;

      .picker-text {
        color: var(--gray-800);
        font-size: 28rpx;
      }

      .picker-icon {
        font-size: 24rpx;
        color: var(--gray-400);
      }
    }
  }

  .modal-footer {
    padding: 24rpx 32rpx 32rpx;
    border-top: 1rpx solid var(--gray-200);
    display: flex;
    gap: 16rpx;

    .cancel-btn {
      flex: 1;
      height: 80rpx;
      background: var(--gray-100);
      color: var(--gray-600);
      border: 1rpx solid var(--gray-200);
    }

    .submit-btn {
      flex: 2;
      height: 80rpx;
    }
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50rpx) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
</style>