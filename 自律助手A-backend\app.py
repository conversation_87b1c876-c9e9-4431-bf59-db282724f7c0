from flask import Flask, request, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_admin import Admin
from flask_admin.contrib.sqla import ModelView
from flask_cors import CORS
from werkzeug.security import generate_password_hash, check_password_hash
import jwt, datetime
from functools import wraps

app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///data.db'
app.config['SECRET_KEY'] = 'your-secret-key'
db = SQLAlchemy(app)
CORS(app)

# 用户模型
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password = db.Column(db.String(128), nullable=False)
    tasks = db.relationship('Task', backref='user', lazy=True)
    profile = db.relationship('Profile', backref='user', uselist=False)

    def __repr__(self):
        return f'<User {self.username}>'

# 用户资料模型
class Profile(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False, unique=True)
    avatar = db.Column(db.String(256), default='/common/images/1.png')
    nickname = db.Column(db.String(64), default='新用户')
    country = db.Column(db.String(32), default='中国🇨🇳')
    phone = db.Column(db.String(32), default='')
    email = db.Column(db.String(64), default='')
    gender = db.Column(db.String(8), default='')
    birthday = db.Column(db.String(16), default='')
    signature = db.Column(db.String(128), default='')

    def to_dict(self):
        return {
            'avatar': self.avatar,
            'nickname': self.nickname,
            'country': self.country,
            'phone': self.phone,
            'email': self.email,
            'gender': self.gender,
            'birthday': self.birthday,
            'signature': self.signature
        }

# 打卡模型
class Punch(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    date = db.Column(db.String(20), nullable=False)
    content = db.Column(db.String(255), default='')
    type = db.Column(db.String(32), default='每日打卡')
    created_at = db.Column(db.String(32), default='')

    def to_dict(self):
        return {
            'id': self.id,
            'date': self.date,
            'content': self.content,
            'type': self.type,
            'created_at': self.created_at
        }

# 计划模型
class Plan(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    title = db.Column(db.String(100), nullable=False)
    remind_time = db.Column(db.String(32), default='')
    repeat = db.Column(db.String(32), default='')
    completed = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.String(32), default='')

    def to_dict(self):
        return {
            'id': self.id,
            'title': self.title,
            'remind_time': self.remind_time,
            'repeat': self.repeat,
            'completed': self.completed,
            'created_at': self.created_at
        }

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False, unique=True)
    avatar = db.Column(db.String(256), default='/common/images/1.png')
    nickname = db.Column(db.String(64), default='新用户')
    country = db.Column(db.String(32), default='中国🇨🇳')
    phone = db.Column(db.String(32), default='')
    email = db.Column(db.String(64), default='')
    gender = db.Column(db.String(8), default='')
    birthday = db.Column(db.String(16), default='')
    signature = db.Column(db.String(128), default='')

    def to_dict(self):
        return {
            'avatar': self.avatar,
            'nickname': self.nickname,
            'country': self.country,
            'phone': self.phone,
            'email': self.email,
            'gender': self.gender,
            'birthday': self.birthday,
            'signature': self.signature
        }

# 任务模型
class Task(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(100), nullable=False)
    completed = db.Column(db.Boolean, default=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

    def __repr__(self):
        return f'<Task {self.title}>'

# 消息模型
class Message(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    username = db.Column(db.String(80), nullable=False)
    content = db.Column(db.String(512), nullable=False)
    created_at = db.Column(db.String(32), default='')

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'username': self.username,
            'content': self.content,
            'created_at': self.created_at
        }

# 帖子模型
class Post(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    username = db.Column(db.String(80), nullable=False)
    avatar = db.Column(db.String(256), default='')
    content = db.Column(db.String(1024), nullable=False)
    created_at = db.Column(db.String(32), default='')
    likes = db.Column(db.Integer, default=0)
    comments = db.relationship('Comment', backref='post', lazy=True, cascade='all, delete-orphan')

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'username': self.username,
            'avatar': self.avatar,
            'content': self.content,
            'created_at': self.created_at,
            'likes': self.likes,
            'comments': [c.to_dict() for c in self.comments]
        }

# 评论模型
class Comment(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    post_id = db.Column(db.Integer, db.ForeignKey('post.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    username = db.Column(db.String(80), nullable=False)
    content = db.Column(db.String(512), nullable=False)
    created_at = db.Column(db.String(32), default='')

    def to_dict(self):
        return {
            'id': self.id,
            'post_id': self.post_id,
            'user_id': self.user_id,
            'username': self.username,
            'content': self.content,
            'created_at': self.created_at
        }

# 管理页面
from flask_admin.babel import lazy_gettext as _

class UserModelView(ModelView):
    column_list = ('id', 'username', 'password')
    column_labels = dict(id='用户ID', username='用户名', password='密码')
    form_columns = ('username', 'password')
    form_labels = dict(username='用户名', password='密码')
    can_create = True
    can_edit = True
    can_delete = True
    name = '用户管理'
    def on_model_change(self, form, model, is_created):
        if form.password.data != model.password:
            model.password = generate_password_hash(form.password.data)

class TaskModelView(ModelView):
    column_list = ('id', 'title', 'completed', 'user_id')
    column_labels = dict(id='任务ID', title='标题', completed='是否完成', user_id='所属用户ID')
    form_columns = ('title', 'completed', 'user_id')
    form_labels = dict(title='标题', completed='是否完成', user_id='所属用户ID')
    can_create = True
    can_edit = True
    can_delete = True
    name = '任务管理'

class ProfileModelView(ModelView):
    column_list = ('id', 'user_id', 'avatar', 'nickname', 'country', 'phone', 'email', 'gender', 'birthday', 'signature')
    column_labels = dict(id='资料ID', user_id='用户ID', avatar='头像', nickname='昵称', country='国家', phone='手机号', email='邮箱', gender='性别', birthday='生日', signature='签名')
    form_columns = ('user_id', 'avatar', 'nickname', 'country', 'phone', 'email', 'gender', 'birthday', 'signature')
    form_labels = dict(user_id='用户ID', avatar='头像', nickname='昵称', country='国家', phone='手机号', email='邮箱', gender='性别', birthday='生日', signature='签名')
    can_create = True
    can_edit = True
    can_delete = True
    name = '用户资料管理'

class PunchModelView(ModelView):
    column_list = ('id', 'user_id', 'date', 'content', 'type', 'created_at')
    column_labels = dict(id='打卡ID', user_id='用户ID', date='日期', content='内容', type='类型', created_at='创建时间')
    form_columns = ('user_id', 'date', 'content', 'type', 'created_at')
    form_labels = dict(user_id='用户ID', date='日期', content='内容', type='类型', created_at='创建时间')
    can_create = True
    can_edit = True
    can_delete = True
    name = '打卡管理'

class PlanModelView(ModelView):
    column_list = ('id', 'user_id', 'title', 'remind_time', 'repeat', 'completed', 'created_at')
    column_labels = dict(id='计划ID', user_id='用户ID', title='标题', remind_time='提醒时间', repeat='重复', completed='是否完成', created_at='创建时间')
    form_columns = ('user_id', 'title', 'remind_time', 'repeat', 'completed', 'created_at')
    form_labels = dict(user_id='用户ID', title='标题', remind_time='提醒时间', repeat='重复', completed='是否完成', created_at='创建时间')
    can_create = True
    can_edit = True
    can_delete = True
    name = '计划管理'
    column_searchable_list = ('title', 'user_id')
    column_filters = ('user_id',)

class MessageModelView(ModelView):
    column_list = ('id', 'user_id', 'username', 'content', 'created_at')
    column_labels = dict(id='消息ID', user_id='用户ID', username='用户名', content='内容', created_at='发送时间')
    form_columns = ('user_id', 'username', 'content', 'created_at')
    form_labels = dict(user_id='用户ID', username='用户名', content='内容', created_at='发送时间')
    can_create = True
    can_edit = True
    can_delete = True
    name = '消息管理'

# 反馈模型
class Feedback(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    content = db.Column(db.String(512), nullable=False)
    created_at = db.Column(db.String(32), default='')

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'content': self.content,
            'created_at': self.created_at
        }

# 激励语录模型
class Motto(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    content = db.Column(db.String(256), nullable=False)
    created_at = db.Column(db.String(32), default='')

    def to_dict(self):
        return {
            'id': self.id,
            'content': self.content,
            'created_at': self.created_at
        }

admin = Admin(app, name='自律助手后台', template_mode='bootstrap3')

class UserModelView(ModelView):
    column_list = ('id', 'username', 'password')
    column_labels = dict(id='用户ID', username='用户名', password='密码')
    form_columns = ('username', 'password')
    form_labels = dict(username='用户名', password='密码')
    can_create = True
    can_edit = True
    can_delete = True
    name = '用户管理'
    def on_model_change(self, form, model, is_created):
        if form.password.data != model.password:
            model.password = generate_password_hash(form.password.data)

class TaskModelView(ModelView):
    column_list = ('id', 'title', 'completed', 'user_id')
    column_labels = dict(id='任务ID', title='标题', completed='是否完成', user_id='所属用户ID')
    form_columns = ('title', 'completed', 'user_id')
    form_labels = dict(title='标题', completed='是否完成', user_id='所属用户ID')
    can_create = True
    can_edit = True
    can_delete = True
    name = '任务管理'

class ProfileModelView(ModelView):
    column_list = ('id', 'user_id', 'avatar', 'nickname', 'country', 'phone', 'email', 'gender', 'birthday', 'signature')
    column_labels = dict(id='资料ID', user_id='用户ID', avatar='头像', nickname='昵称', country='国家', phone='手机号', email='邮箱', gender='性别', birthday='生日', signature='签名')
    form_columns = ('user_id', 'avatar', 'nickname', 'country', 'phone', 'email', 'gender', 'birthday', 'signature')
    form_labels = dict(user_id='用户ID', avatar='头像', nickname='昵称', country='国家', phone='手机号', email='邮箱', gender='性别', birthday='生日', signature='签名')
    can_create = True
    can_edit = True
    can_delete = True
    name = '用户资料管理'

class PunchModelView(ModelView):
    column_list = ('id', 'user_id', 'date', 'content', 'type', 'created_at')
    column_labels = dict(id='打卡ID', user_id='用户ID', date='日期', content='内容', type='类型', created_at='创建时间')
    form_columns = ('user_id', 'date', 'content', 'type', 'created_at')
    form_labels = dict(user_id='用户ID', date='日期', content='内容', type='类型', created_at='创建时间')
    can_create = True
    can_edit = True
    can_delete = True
    name = '打卡管理'

class PlanModelView(ModelView):
    column_list = ('id', 'user_id', 'title', 'remind_time', 'repeat', 'completed', 'created_at')
    column_labels = dict(id='计划ID', user_id='用户ID', title='标题', remind_time='提醒时间', repeat='重复', completed='是否完成', created_at='创建时间')
    form_columns = ('user_id', 'title', 'remind_time', 'repeat', 'completed', 'created_at')
    form_labels = dict(user_id='用户ID', title='标题', remind_time='提醒时间', repeat='重复', completed='是否完成', created_at='创建时间')
    can_create = True
    can_edit = True
    can_delete = True
    name = '计划管理'
    column_searchable_list = ('title', 'user_id')
    column_filters = ('user_id',)

class MessageModelView(ModelView):
    column_list = ('id', 'user_id', 'username', 'content', 'created_at')
    column_labels = dict(id='消息ID', user_id='用户ID', username='用户名', content='内容', created_at='发送时间')
    form_columns = ('user_id', 'username', 'content', 'created_at')
    form_labels = dict(user_id='用户ID', username='用户名', content='内容', created_at='发送时间')
    can_create = True
    can_edit = True
    can_delete = True
    name = '消息管理'

class PostModelView(ModelView):
    column_list = ('id', 'user_id', 'username', 'avatar', 'content', 'created_at', 'likes')
    column_labels = dict(id='帖子ID', user_id='用户ID', username='用户名', avatar='头像', content='内容', created_at='发布时间', likes='点赞数')
    form_columns = ('user_id', 'username', 'avatar', 'content', 'created_at', 'likes')
    form_labels = dict(user_id='用户ID', username='用户名', avatar='头像', content='内容', created_at='发布时间', likes='点赞数')
    can_create = True
    can_edit = True
    can_delete = True
    name = '帖子管理'

class CommentModelView(ModelView):
    column_list = ('id', 'post_id', 'user_id', 'username', 'content', 'created_at')
    column_labels = dict(id='评论ID', post_id='帖子ID', user_id='用户ID', username='用户名', content='内容', created_at='评论时间')
    form_columns = ('post_id', 'user_id', 'username', 'content', 'created_at')
    form_labels = dict(post_id='帖子ID', user_id='用户ID', username='用户名', content='内容', created_at='评论时间')
    can_create = True
    can_edit = True
    can_delete = True
    name = '评论管理'

class FeedbackModelView(ModelView):
    column_list = ('id', 'user_id', 'content', 'created_at')
    column_labels = dict(id='反馈ID', user_id='用户ID', content='反馈内容', created_at='提交时间')
    form_columns = ('user_id', 'content', 'created_at')
    form_labels = dict(user_id='用户ID', content='反馈内容', created_at='提交时间')
    can_create = True
    can_edit = True
    can_delete = True
    name = '用户反馈'

class MottoModelView(ModelView):
    column_list = ('id', 'content', 'created_at')
    column_labels = dict(id='语录ID', content='语录内容', created_at='添加时间')
    form_columns = ('content', 'created_at')
    form_labels = dict(content='语录内容', created_at='添加时间')
    can_create = True
    can_edit = True
    can_delete = True
    name = '激励语录'

admin.add_view(UserModelView(User, db.session, name='用户管理'))
admin.add_view(TaskModelView(Task, db.session, name='任务管理'))
admin.add_view(ProfileModelView(Profile, db.session, name='用户资料管理'))
admin.add_view(PunchModelView(Punch, db.session, name='打卡管理'))
admin.add_view(PlanModelView(Plan, db.session, name='计划管理'))
admin.add_view(MessageModelView(Message, db.session, name='消息管理'))
admin.add_view(PostModelView(Post, db.session, name='帖子管理'))
admin.add_view(CommentModelView(Comment, db.session, name='评论管理'))
admin.add_view(FeedbackModelView(Feedback, db.session, name='用户反馈'))
admin.add_view(MottoModelView(Motto, db.session, name='激励语录'))

# JWT工具
SECRET_KEY = app.config['SECRET_KEY']
def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None
        if 'Authorization' in request.headers:
            auth_header = request.headers['Authorization']
            if auth_header.startswith('Bearer '):
                token = auth_header.split(' ')[1]
        if not token:
            return jsonify({'msg': 'Token缺失'}), 401
        try:
            data = jwt.decode(token, SECRET_KEY, algorithms=["HS256"])
            current_user = User.query.get(data['user_id'])
            if not current_user:
                raise Exception('用户不存在')
        except Exception as e:
            return jsonify({'msg': 'Token无效或已过期'}), 401
        return f(current_user, *args, **kwargs)
    return decorated

# 注册接口
@app.route('/api/register', methods=['POST'])
def register():
    data = request.json
    print('注册请求，username:', data.get('username'))  # 日志

    if User.query.filter_by(username=data['username']).first():
        return jsonify({'msg': '用户名已存在'}), 400
    hashed_pw = generate_password_hash(data['password'])
    new_user = User(username=data['username'], password=hashed_pw)
    db.session.add(new_user)
    db.session.commit()
    return jsonify({'msg': '注册成功'})

# 登录接口
@app.route('/api/login', methods=['POST'])
def login():
    data = request.json
    user = User.query.filter_by(username=data.get('username')).first()
    if user and check_password_hash(user.password, data.get('password')):
        token = jwt.encode({
            'user_id': user.id,
            'exp': datetime.datetime.utcnow() + datetime.timedelta(hours=24)
        }, SECRET_KEY, algorithm='HS256')
        return jsonify({'token': token})
    return jsonify({'msg': '用户名或密码错误'}), 401

# 忘记密码-重置密码（调试用）
@app.route('/api/reset_pwd', methods=['POST'])
def reset_pwd():
    data = request.json
    user = User.query.filter_by(username=data.get('username')).first()
    if not user:
        return jsonify({'msg': '用户不存在'}), 404
    user.password = generate_password_hash(data.get('new_password'))
    db.session.commit()
    return jsonify({'msg': '密码重置成功'})

# 获取当前用户信息
@app.route('/api/me', methods=['GET'])
@token_required
def me(current_user):
    return jsonify({'username': current_user.username})

# 获取和更新用户资料
@app.route('/api/profile', methods=['GET', 'POST'])
@token_required
def profile(current_user):
    prof = Profile.query.filter_by(user_id=current_user.id).first()
    if not prof:
        # 自动创建默认资料
        prof = Profile(user_id=current_user.id)
        db.session.add(prof)
        db.session.commit()
    if request.method == 'GET':
        return jsonify(prof.to_dict())
    else:
        data = request.json
        for field in ['avatar', 'nickname', 'country', 'phone', 'email', 'gender', 'birthday', 'signature']:
            if field in data:
                setattr(prof, field, data[field])
        db.session.commit()
        return jsonify({'msg': '资料更新成功', **prof.to_dict()})

# 打卡API（仅操作自己数据）
@app.route('/api/punches', methods=['GET'])
@token_required
def get_punches(current_user):
    punches = Punch.query.filter_by(user_id=current_user.id).all()
    return jsonify([p.to_dict() for p in punches])

@app.route('/api/punches', methods=['POST'])
@token_required
def add_punch(current_user):
    data = request.json
    punch = Punch(user_id=current_user.id, date=data.get('date'), content=data.get('content', ''), type=data.get('type', '每日打卡'), created_at=data.get('created_at', ''))
    db.session.add(punch)
    db.session.commit()
    return jsonify({'msg': '打卡成功', 'id': punch.id})

@app.route('/api/punches/<int:punch_id>', methods=['PUT', 'PATCH'])
@token_required
def update_punch(current_user, punch_id):
    punch = Punch.query.filter_by(id=punch_id, user_id=current_user.id).first_or_404()
    data = request.json
    for field in ['date', 'content', 'type', 'created_at']:
        if field in data:
            setattr(punch, field, data[field])
    db.session.commit()
    return jsonify({'msg': '修改成功'})

@app.route('/api/punches/<int:punch_id>', methods=['DELETE'])
@token_required
def delete_punch(current_user, punch_id):
    punch = Punch.query.filter_by(id=punch_id, user_id=current_user.id).first_or_404()
    db.session.delete(punch)
    db.session.commit()
    return jsonify({'msg': '删除成功'})

# 计划API（仅操作自己数据）
@app.route('/api/plans', methods=['GET'])
@token_required
def get_plans(current_user):
    plans = Plan.query.filter_by(user_id=current_user.id).all()
    return jsonify([p.to_dict() for p in plans])

@app.route('/api/plans', methods=['POST'])
@token_required
def add_plan(current_user):
    data = request.json
    print(f"[新增计划] user_id={current_user.id}, title={data.get('title')}, data={data}")
    plan = Plan(user_id=current_user.id, title=data.get('title'), remind_time=data.get('remind_time', ''), repeat=data.get('repeat', ''), completed=data.get('completed', False), created_at=data.get('created_at', ''))
    db.session.add(plan)
    db.session.commit()
    print(f"[新增计划成功] id={plan.id}, user_id={plan.user_id}")
    return jsonify({'msg': '添加成功', **plan.to_dict(), 'user_id': plan.user_id, 'id': plan.id})

@app.route('/api/plans/<int:plan_id>', methods=['PUT', 'PATCH'])
@token_required
def update_plan(current_user, plan_id):
    plan = Plan.query.filter_by(id=plan_id, user_id=current_user.id).first_or_404()
    data = request.json
    print(f"[修改计划] user_id={current_user.id}, plan_id={plan_id}, data={data}")
    for field in ['title', 'remind_time', 'repeat', 'completed', 'created_at']:
        if field in data:
            setattr(plan, field, data[field])
    db.session.commit()
    print(f"[修改计划成功] id={plan.id}, user_id={plan.user_id}")
    return jsonify({'msg': '修改成功', **plan.to_dict(), 'user_id': plan.user_id, 'id': plan.id})

@app.route('/api/plans/<int:plan_id>', methods=['DELETE'])
@token_required
def delete_plan(current_user, plan_id):
    plan = Plan.query.filter_by(id=plan_id, user_id=current_user.id).first_or_404()
    db.session.delete(plan)
    db.session.commit()
    return jsonify({'msg': '删除成功'})

# 任务API（仅操作自己数据）
@app.route('/api/tasks', methods=['GET'])
@token_required
def get_tasks(current_user):
    tasks = Task.query.filter_by(user_id=current_user.id).all()
    return jsonify([
        {'id': t.id, 'title': t.title, 'completed': t.completed} for t in tasks
    ])

@app.route('/api/tasks', methods=['POST'])
@token_required
def add_task(current_user):
    data = request.json
    task = Task(title=data.get('title'), completed=data.get('completed', False), user_id=current_user.id)
    db.session.add(task)
    db.session.commit()
    return jsonify({'id': task.id, 'title': task.title, 'completed': task.completed})

@app.route('/api/tasks/<int:task_id>', methods=['PUT'])
@token_required
def update_task(current_user, task_id):
    task = Task.query.filter_by(id=task_id, user_id=current_user.id).first()
    if not task:
        return jsonify({'msg': '任务不存在'}), 404
    data = request.json
    task.title = data.get('title', task.title)
    task.completed = data.get('completed', task.completed)
    db.session.commit()
    return jsonify({'msg': '更新成功'})

@app.route('/api/tasks/<int:task_id>', methods=['DELETE'])
@token_required
def delete_task(current_user, task_id):
    task = Task.query.filter_by(id=task_id, user_id=current_user.id).first()
    if not task:
        return jsonify({'msg': '任务不存在'}), 404
    db.session.delete(task)
    db.session.commit()
    return jsonify({'msg': '删除成功'})


# 消息API
@app.route('/api/messages', methods=['GET'])
@token_required
def get_messages(current_user):
    messages = Message.query.order_by(Message.created_at.desc()).limit(100).all()
    return jsonify([m.to_dict() for m in messages][::-1])

@app.route('/api/messages', methods=['POST'])
@token_required
def send_message(current_user):
    data = request.json
    content = data.get('content', '')
    import datetime
    msg = Message(user_id=current_user.id, username=current_user.username, content=content, created_at=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    db.session.add(msg)
    db.session.commit()
    return jsonify(msg.to_dict())

# 帖子API
from flask import abort
import datetime

@app.route('/api/posts', methods=['GET'])
@token_required
def get_posts(current_user):
    posts = Post.query.order_by(Post.created_at.desc()).all()
    return jsonify([p.to_dict() for p in posts])

@app.route('/api/posts', methods=['POST'])
@token_required
def add_post(current_user):
    data = request.json
    content = data.get('content', '')
    avatar = data.get('avatar', '')
    post = Post(user_id=current_user.id, username=current_user.username, avatar=avatar, content=content, created_at=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    db.session.add(post)
    db.session.commit()
    return jsonify(post.to_dict())

@app.route('/api/posts/<int:post_id>', methods=['DELETE'])
@token_required
def delete_post(current_user, post_id):
    post = Post.query.get_or_404(post_id)
    if post.user_id != current_user.id:
        abort(403)
    db.session.delete(post)
    db.session.commit()
    return jsonify({'msg': '删除成功'})

@app.route('/api/posts/<int:post_id>/like', methods=['POST'])
@token_required
def like_post(current_user, post_id):
    post = Post.query.get_or_404(post_id)
    post.likes += 1
    db.session.commit()
    return jsonify({'likes': post.likes})

# 评论API
@app.route('/api/comments', methods=['POST'])
@token_required
def add_comment(current_user):
    data = request.json
    post_id = data.get('post_id')
    content = data.get('content', '')
    comment = Comment(post_id=post_id, user_id=current_user.id, username=current_user.username, content=content, created_at=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    db.session.add(comment)
    db.session.commit()
    return jsonify(comment.to_dict())

@app.route('/api/comments/<int:comment_id>', methods=['DELETE'])
@token_required
def delete_comment(current_user, comment_id):
    comment = Comment.query.get_or_404(comment_id)
    if comment.user_id != current_user.id:
        abort(403)
    db.session.delete(comment)
    db.session.commit()
    return jsonify({'msg': '删除成功'})

# 时间提醒模型
class Reminder(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    title = db.Column(db.String(100), nullable=False)
    date = db.Column(db.String(16), nullable=False)
    time = db.Column(db.String(8), nullable=False)
    repeat = db.Column(db.String(32), default='')

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'title': self.title,
            'date': self.date,
            'time': self.time,
            'repeat': self.repeat
        }

# 好友关系模型
class Friend(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    friend_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_at = db.Column(db.String(32), default='')

    def to_dict(self):
        friend_user = User.query.get(self.friend_id)
        profile = Profile.query.filter_by(user_id=self.friend_id).first()
        return {
            'id': self.id,
            'user_id': self.user_id,
            'friend_id': self.friend_id,
            'friend_username': friend_user.username if friend_user else '',
            'avatar': profile.avatar if profile else '',
            'nickname': profile.nickname if profile else '',
            'country': profile.country if profile else '',
            'created_at': self.created_at
        }

# 好友API
@app.route('/api/friends', methods=['GET'])
@token_required
def get_friends(current_user):
    friends = Friend.query.filter_by(user_id=current_user.id).all()
    return jsonify([f.to_dict() for f in friends])

@app.route('/api/friends', methods=['POST'])
@token_required
def add_friend(current_user):
    data = request.json
    username = data.get('username', '').strip()
    if not username:
        return jsonify({'error': '用户名不能为空'}), 400
    if username == current_user.username:
        return jsonify({'error': '不能添加自己为好友'}), 400
    friend_user = User.query.filter_by(username=username).first()
    if not friend_user:
        return jsonify({'error': '用户不存在'}), 404
    # 检查是否已是好友
    exist = Friend.query.filter_by(user_id=current_user.id, friend_id=friend_user.id).first()
    if exist:
        return jsonify({'error': '已添加为好友'}), 400
    import datetime
    now = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    new_friend = Friend(user_id=current_user.id, friend_id=friend_user.id, created_at=now)
    db.session.add(new_friend)
    db.session.commit()
    return jsonify(new_friend.to_dict())

@app.route('/api/friends/<int:friend_id>', methods=['DELETE'])
@token_required
def delete_friend(current_user, friend_id):
    friend = Friend.query.filter_by(user_id=current_user.id, friend_id=friend_id).first()
    if not friend:
        return jsonify({'error': '好友关系不存在'}), 404
    db.session.delete(friend)
    db.session.commit()
    return jsonify({'msg': '删除成功'})

# 连续打卡排行榜API
@app.route('/api/rankings/streak', methods=['GET'])
def streak_ranking():
    # 统计所有用户的最大连续打卡天数
    users = User.query.all()
    results = []
    for user in users:
        punches = Punch.query.filter_by(user_id=user.id).order_by(Punch.date.asc()).all()
        if not punches:
            continue
        # 计算最大连续天数
        streak, max_streak = 0, 0
        prev_date = None
        for punch in punches:
            try:
                cur_date = datetime.datetime.strptime(punch.date, '%Y-%m-%d').date()
            except Exception:
                continue
            if prev_date is None or (cur_date - prev_date).days == 1:
                streak += 1
            elif (cur_date - prev_date).days == 0:
                continue
            else:
                streak = 1
            prev_date = cur_date
            max_streak = max(max_streak, streak)
        # 用户资料
        profile = Profile.query.filter_by(user_id=user.id).first()
        results.append({
            'user_id': user.id,
            'username': user.username,
            'avatar': profile.avatar if profile else '',
            'nickname': profile.nickname if profile else '',
            'country': profile.country if profile else '',
            'streak_days': max_streak
        })
    # 按连续天数降序
    results.sort(key=lambda x: x['streak_days'], reverse=True)
    return jsonify(results)

# 反馈API
@app.route('/api/feedback', methods=['POST'])
@token_required
def add_feedback(current_user):
    data = request.json
    content = data.get('content', '').strip()
    if not content:
        return jsonify({'error': '反馈内容不能为空'}), 400
    import datetime
    now = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    feedback = Feedback(user_id=current_user.id, content=content, created_at=now)
    db.session.add(feedback)
    db.session.commit()
    return jsonify(feedback.to_dict())

@app.route('/api/feedback', methods=['GET'])
def get_feedback():
    # 管理员权限判断可自行扩展，这里默认允许
    feedbacks = Feedback.query.order_by(Feedback.created_at.desc()).all()
    return jsonify([f.to_dict() for f in feedbacks])

# 激励语录API
@app.route('/api/mottos', methods=['GET'])
def get_mottos():
    mottos = Motto.query.order_by(Motto.created_at.desc()).all()
    return jsonify([m.to_dict() for m in mottos])

@app.route('/api/mottos', methods=['POST'])
def add_motto():
    # 管理员权限判断可自行扩展，这里默认允许
    data = request.json
    content = data.get('content', '').strip()
    if not content:
        return jsonify({'error': '语录内容不能为空'}), 400
    import datetime
    now = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    motto = Motto(content=content, created_at=now)
    db.session.add(motto)
    db.session.commit()
    return jsonify(motto.to_dict())

# 时间提醒API（仅操作自己数据）
@app.route('/api/reminders', methods=['GET'])
@token_required
def get_reminders(current_user):
    reminders = Reminder.query.filter_by(user_id=current_user.id).all()
    return jsonify([r.to_dict() for r in reminders])

@app.route('/api/reminders', methods=['POST'])
@token_required
def add_reminder(current_user):
    data = request.json
    title = data.get('title', '').strip()
    date = data.get('date', '').strip()
    time_ = data.get('time', '').strip()
    repeat = data.get('repeat', '').strip()
    if not title or not date or not time_:
        return jsonify({'error': '内容、日期和时间不能为空'}), 400
    reminder = Reminder(user_id=current_user.id, title=title, date=date, time=time_, repeat=repeat)
    db.session.add(reminder)
    db.session.commit()
    return jsonify(reminder.to_dict())

@app.route('/api/reminders/<int:reminder_id>', methods=['PUT'])
@token_required
def update_reminder(current_user, reminder_id):
    reminder = Reminder.query.filter_by(id=reminder_id, user_id=current_user.id).first()
    if not reminder:
        return jsonify({'error': '提醒不存在'}), 404
    data = request.json
    reminder.title = data.get('title', reminder.title)
    reminder.date = data.get('date', reminder.date)
    reminder.time = data.get('time', reminder.time)
    reminder.repeat = data.get('repeat', reminder.repeat)
    db.session.commit()
    return jsonify(reminder.to_dict())

@app.route('/api/reminders/<int:reminder_id>', methods=['DELETE'])
@token_required
def delete_reminder(current_user, reminder_id):
    reminder = Reminder.query.filter_by(id=reminder_id, user_id=current_user.id).first()
    if not reminder:
        return jsonify({'error': '提醒不存在'}), 404
    db.session.delete(reminder)
    db.session.commit()
    return jsonify({'msg': '删除成功'})

# 启动时自动建表，防止新表缺失
with app.app_context():
    db.create_all()

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
