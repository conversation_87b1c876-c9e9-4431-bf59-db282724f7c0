<template>
  <view class="study-records">
    <!-- 标题 -->
    <view class="header">
      <text class="header-title">学习记录</text>
    </view>

    <!-- 记录列表 -->
    <view class="record-list">
      <view class="record-item" v-for="(record, index) in records" :key="index">
        <view class="record-date">
          <text>{{ record.date }}</text>
        </view>
        <view class="record-details">
          <text class="record-title">{{ record.title }}</text>
          <text class="record-duration">时长: {{ record.duration }}小时</text>
          <text class="record-desc">{{ record.description }}</text>
        </view>
      </view>
      <view class="no-records" v-if="records.length === 0">
        <text>暂无学习记录</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';

// 学习记录数据
const records = ref([
  {
    date: '2025-03-21',
    title: '学习 Vue 3',
    duration: 2.5,
    description: '完成了组件开发部分的学习',
  },
  {
    date: '2025-03-20',
    title: '跑步锻炼',
    duration: 1,
    description: '跑了5公里，感觉很棒',
  },
]);
</script>

<style lang="scss">
.study-records {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  margin-bottom: 30rpx;
  .header-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.record-list {
  .record-item {
    display: flex;
    background-color: #fff;
    padding: 20rpx;
    border-radius: 20rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    .record-date {
      width: 160rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28rpx;
      color: #666;
      border-right: 2rpx solid #eee;
    }
    .record-details {
      flex: 1;
      padding-left: 20rpx;
      .record-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        display: block;
        margin-bottom: 10rpx;
      }
      .record-duration {
        font-size: 28rpx;
        color: #007aff;
        display: block;
        margin-bottom: 10rpx;
      }
      .record-desc {
        font-size: 26rpx;
        color: #666;
      }
    }
  }
  .no-records {
    text-align: center;
    font-size: 32rpx;
    color: #666;
    padding: 40rpx;
  }
}
</style>