<template>
  <view class="profile">
    <!-- 标题 -->
    <view class="header">
      <text class="header-title">个人信息</text>
    </view>

    <!-- 用户信息 -->
    <view class="user-info">
      <view class="avatar-container">
        <image :src="userInfo.avatar" mode="aspectFill" class="avatar"></image>
        <button class="edit-avatar" @click="editAvatar">更换头像</button>
      </view>
      <view class="info-item">
        <text class="info-label">用户名</text>
        <input class="info-input" v-model="userInfo.username" placeholder="请输入用户名" />
      </view>
      <view class="info-item">
        <text class="info-label">邮箱</text>
        <input class="info-input" v-model="userInfo.email" placeholder="请输入邮箱" />
      </view>
      <view class="info-item">
        <text class="info-label">简介</text>
        <textarea class="info-textarea" v-model="userInfo.bio" placeholder="请输入个人简介"></textarea>
      </view>
      <button class="save-button" @click="saveProfile">保存</button>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';

// 用户信息
const userInfo = ref({
  avatar: '/static/images/avatar.png',
  username: '自律达人',
  email: '<EMAIL>',
  bio: '热爱学习，追求进步！',
});

// 更换头像（模拟功能）
const editAvatar = () => {
  console.log('更换头像功能待实现');
  // 可调用 uni.chooseImage 或后端接口
};

// 保存个人信息
const saveProfile = () => {
  console.log('保存个人信息:', userInfo.value);
};
</script>

<style lang="scss">
.profile {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  margin-bottom: 30rpx;
  .header-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.user-info {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  .avatar-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 40rpx;
    .avatar {
      width: 160rpx;
      height: 160rpx;
      border-radius: 80rpx;
      border: 4rpx solid #007aff;
      margin-bottom: 20rpx;
    }
    .edit-avatar {
      height: 60rpx;
      padding: 0 20rpx;
      background-color: #007aff;
      color: #fff;
      border: none;
      border-radius: 10rpx;
      font-size: 28rpx;
    }
  }
  .info-item {
    margin-bottom: 30rpx;
    .info-label {
      font-size: 28rpx;
      color: #666;
      display: block;
      margin-bottom: 10rpx;
    }
    .info-input {
      width: 100%;
      height: 80rpx;
      border: 2rpx solid #ddd;
      border-radius: 10rpx;
      padding: 0 20rpx;
      font-size: 28rpx;
    }
    .info-textarea {
      width: 100%;
      height: 160rpx;
      border: 2rpx solid #ddd;
      border-radius: 10rpx;
      padding: 20rpx;
      font-size: 28rpx;
    }
  }
  .save-button {
    width: 100%;
    height: 80rpx;
    background-color: #007aff;
    color: #fff;
    border: none;
    border-radius: 10rpx;
    font-size: 32rpx;
  }
}
</style>