# 故障排除指南

## 常见错误及解决方案

### 1. "message port closed" 错误

**错误信息：**
```
Unchecked runtime.lastError: The message port closed before a response was received.
```

**原因：**
这是Chrome浏览器扩展程序导致的常见错误，不影响应用功能。

**解决方案：**
- ✅ **已自动处理** - 应用已添加错误过滤，会自动忽略此类错误
- 如果仍然困扰，可以禁用相关浏览器扩展
- 或者在开发者工具中忽略此类错误

### 2. "video元素未找到" 错误

**错误信息：**
```
video元素未找到
```

**原因：**
DOM渲染时序问题，video元素还未完全加载。

**解决方案：**
- ✅ **已自动处理** - 应用已添加重试机制和延迟处理
- 如果仍有问题，请刷新页面重试
- 确保网络连接稳定

### 3. 摄像头无法启动

**可能原因：**
- 浏览器不支持摄像头功能
- 摄像头权限被拒绝
- 摄像头被其他应用占用
- 设备没有摄像头

**解决方案：**

#### 检查浏览器支持
- 使用Chrome 53+、Firefox 36+、Safari 11+、Edge 79+
- 避免使用IE或旧版Edge

#### 检查权限设置
1. **Chrome/Edge:**
   - 点击地址栏左侧的锁图标
   - 确保"摄像头"权限设置为"允许"
   - 刷新页面

2. **Firefox:**
   - 点击地址栏左侧的盾牌图标
   - 允许摄像头访问权限

3. **Safari:**
   - Safari > 偏好设置 > 网站 > 摄像头
   - 设置为"允许"

#### 检查设备状态
- 确保摄像头未被其他应用占用
- 重启浏览器
- 重启设备

### 4. AI分析失败

**错误信息：**
```
分析失败，使用随机数据
```

**可能原因：**
- 网络连接问题
- API服务暂时不可用
- 图像质量问题

**解决方案：**
- 检查网络连接
- 确保能访问Google服务
- 改善光线条件
- 确保面部清晰可见
- 重试拍照分析

### 5. 拍照功能异常

**可能原因：**
- video元素未准备就绪
- canvas渲染失败
- 浏览器兼容性问题

**解决方案：**
- 等待摄像头完全加载后再拍照
- 检查浏览器控制台错误信息
- 尝试刷新页面
- 切换到支持的浏览器

## 调试步骤

### 1. 检查控制台
打开浏览器开发者工具（F12），查看Console标签页：
- 红色错误信息 - 需要处理的问题
- 黄色警告信息 - 可能的问题
- 蓝色信息 - 正常的调试信息

### 2. 检查网络
在开发者工具的Network标签页中：
- 查看API请求是否成功
- 检查请求响应时间
- 确认没有网络错误

### 3. 检查权限
在浏览器设置中：
- 确认摄像头权限已授予
- 检查是否有其他权限限制
- 尝试重新授权

### 4. 测试功能
按以下顺序测试：
1. 页面是否正常加载
2. 摄像头是否能正常启动
3. 拍照功能是否正常
4. AI分析是否能返回结果

## 环境要求

### 浏览器要求
- **推荐：** Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **最低：** Chrome 53+, Firefox 36+, Safari 11+, Edge 79+
- **不支持：** IE, Edge Legacy (18及以下)

### 网络要求
- 稳定的互联网连接
- 能够访问Google服务
- HTTPS环境（摄像头功能要求）

### 设备要求
- 具备摄像头的设备
- 支持WebRTC的浏览器
- 足够的内存和处理能力

## 性能优化建议

### 1. 浏览器优化
- 关闭不必要的浏览器扩展
- 清理浏览器缓存
- 更新到最新版本

### 2. 网络优化
- 使用稳定的WiFi连接
- 避免网络高峰期使用
- 检查防火墙设置

### 3. 设备优化
- 确保设备性能充足
- 关闭其他占用摄像头的应用
- 保持良好的光线环境

## 联系支持

如果以上方案都无法解决问题，请提供以下信息：

1. **浏览器信息：** 浏览器类型和版本号
2. **操作系统：** 系统类型和版本
3. **错误信息：** 完整的错误消息
4. **复现步骤：** 详细的操作步骤
5. **控制台日志：** 开发者工具中的错误信息

这些信息将帮助快速定位和解决问题。
