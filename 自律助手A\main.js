import App from './App'
import tabbarFix from './utils/tabbarFix.js'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
export function createApp() {
  const app = createSSRApp(App)

  // 在应用启动时初始化TabBar修复
  app.config.globalProperties.$tabbarFix = tabbarFix

  return {
    app
  }
}
// #endif