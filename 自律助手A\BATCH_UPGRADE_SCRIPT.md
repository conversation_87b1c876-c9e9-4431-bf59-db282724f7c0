# 批量升级脚本

## 剩余待升级页面

### 简单页面 (只需要基础模板)
1. **schedule** - 进度追踪
2. **dataanalysis** - 数据分析  
3. **evaluate** - 评价
4. **achievementwall** - 成就墙
5. **saying** - 激励语录
6. **rankinglist** - 排行榜
7. **friends** - 好友
8. **social** - 社交动态
9. **setting** - 设置
10. **nation** - 国家/地区

### 复杂页面 (需要特殊处理)
1. **user** - 用户中心
2. **login** - 登录页面

## 基础升级模板

### HTML结构模板
```vue
<template>
  <view class="modern-page">
    <!-- 头部区域 -->
    <view class="modern-header">
      <text class="header-title">页面标题</text>
      <text class="header-subtitle">页面副标题</text>
      <view class="header-decoration"></view>
    </view>

    <!-- 内容区域 -->
    <view class="modern-content">
      <!-- 功能卡片网格 -->
      <view class="modern-grid"> <!-- 或 modern-grid three-column -->
        <navigator url="/pages/xxx/xxx" class="feature-card-nav">
          <view class="feature-card xxx-card fade-in-up">
            <view class="card-icon">
              <text class="icon-text">🎯</text>
            </view>
            <view class="card-content">
              <text class="card-title">功能标题</text>
              <text class="card-desc">功能描述</text>
            </view>
            <view class="card-arrow">
              <text class="arrow-icon">→</text>
            </view>
          </view>
        </navigator>
      </view>
      
      <!-- 或者内容卡片 -->
      <view class="modern-card content-card fade-in-up">
        <view class="card-header">
          <text class="card-title">内容标题</text>
        </view>
        <view class="card-body">
          <!-- 具体内容 -->
        </view>
      </view>
    </view>

    <!-- 底部装饰 -->
    <view class="bottom-decoration"></view>
  </view>
</template>
```

### 样式模板
```scss
<style lang="scss">
// 使用全局现代化主题样式
.feature-card-nav {
  text-decoration: none;
}

.bottom-decoration {
  height: 100rpx;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.1), transparent);
  position: relative;
  z-index: 1;
}

// 页面特定样式
.content-card {
  .card-header {
    padding-bottom: 20rpx;
    border-bottom: 1rpx solid var(--gray-200);
    margin-bottom: 24rpx;
    
    .card-title {
      font-size: 28rpx;
      font-weight: 600;
      color: var(--gray-800);
    }
  }
  
  .card-body {
    // 具体内容样式
  }
}
</style>
```

## 页面特定配置

### schedule (进度追踪)
- 主题色: plan-card (蓝紫色)
- 图标: 📊
- 副标题: "可视化学习进度，掌握学习节奏"

### dataanalysis (数据分析)
- 主题色: monitor-card (橙色)
- 图标: 📈
- 副标题: "深度分析学习数据，优化学习策略"

### evaluate (评价)
- 主题色: achievement-card (红色)
- 图标: ⭐
- 副标题: "全面评估学习表现，持续改进提升"

### achievementwall (成就墙)
- 主题色: achievement-card (红色)
- 图标: 🏆
- 副标题: "展示学习成就，激励持续进步"

### saying (激励语录)
- 主题色: checkin-card (绿色)
- 图标: 💡
- 副标题: "每日正能量，点亮学习之路"

### rankinglist (排行榜)
- 主题色: social-card (青色)
- 图标: 🏅
- 副标题: "学习排行榜，与伙伴一起进步"

### friends (好友)
- 主题色: social-card (青色)
- 图标: 👥
- 副标题: "添加学习伙伴，共同成长进步"

### social (社交动态)
- 主题色: social-card (青色)
- 图标: 💬
- 副标题: "分享学习心得，交流学习经验"

### setting (设置)
- 主题色: profile-card (紫色)
- 图标: ⚙️
- 副标题: "个性化设置，打造专属学习环境"

### nation (国家/地区)
- 主题色: profile-card (紫色)
- 图标: 🌍
- 副标题: "选择您的国家和地区"

## 升级步骤

1. **替换模板结构** - 使用现代化页面结构
2. **更新样式** - 应用全局主题样式
3. **保持功能完整** - 确保原有功能不受影响
4. **测试验证** - 检查页面显示和功能正常

## 注意事项

- 保持原有的script部分不变
- 只更新template和style部分
- 确保所有功能正常工作
- 统一使用现代化设计语言
