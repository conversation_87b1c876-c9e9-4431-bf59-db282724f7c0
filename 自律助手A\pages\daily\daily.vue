<template>
  <view class="modern-page">
    <!-- 头部区域 -->
    <view class="modern-header">
      <text class="header-title">每日管理</text>
      <text class="header-subtitle">坚持每一天，成就更好的自己</text>
      <view class="header-decoration"></view>
    </view>

    <!-- 内容区域 -->
    <view class="modern-content">
      <view class="modern-grid three-column">
        <!-- 每日打卡卡片 -->
        <navigator url="/pages/dailypunch/dailypunch" class="feature-card-nav">
          <view class="feature-card checkin-card fade-in-up">
            <view class="card-icon">
              <text class="icon-text">✓</text>
            </view>
            <view class="card-content">
              <text class="card-title">每日打卡</text>
              <text class="card-desc">养成良好习惯</text>
            </view>
            <view class="card-arrow">
              <text class="arrow-icon">→</text>
            </view>
          </view>
        </navigator>

        <!-- 进度追踪卡片 -->
        <navigator url="/pages/schedule/schedule" class="feature-card-nav">
          <view class="feature-card plan-card fade-in-up">
            <view class="card-icon">
              <text class="icon-text">📈</text>
            </view>
            <view class="card-content">
              <text class="card-title">进度追踪</text>
              <text class="card-desc">实时查看进度</text>
            </view>
            <view class="card-arrow">
              <text class="arrow-icon">→</text>
            </view>
          </view>
        </navigator>

        <!-- 数据分析卡片 -->
        <navigator url="/pages/dataanalysis/dataanalysis" class="feature-card-nav">
          <view class="feature-card monitor-card fade-in-up">
            <view class="card-icon">
              <text class="icon-text">📊</text>
            </view>
            <view class="card-content">
              <text class="card-title">数据分析</text>
              <text class="card-desc">深度数据洞察</text>
            </view>
            <view class="card-arrow">
              <text class="arrow-icon">→</text>
            </view>
          </view>
        </navigator>
      </view>
    </view>

    <!-- 底部装饰 -->
    <view class="bottom-decoration"></view>
  </view>
</template>

<script setup>
// 可以在这里添加逻辑代码
</script>

<style lang="scss">
// 使用全局现代化主题样式
.feature-card-nav {
  text-decoration: none;
}

.bottom-decoration {
  height: 100rpx;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.1), transparent);
  position: relative;
  z-index: 1;
}
</style>