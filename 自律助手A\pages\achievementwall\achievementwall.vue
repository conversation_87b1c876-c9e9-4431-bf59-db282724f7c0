<template>
  <view class="modern-page">
    <!-- 头部区域 -->
    <view class="modern-header">
      <text class="header-title">成就墙</text>
      <text class="header-subtitle">展示学习成就，激励持续进步</text>
      <view class="header-decoration"></view>
    </view>

    <!-- 内容区域 -->
    <view class="modern-content">
      <!-- 成就统计卡片 -->
      <view class="modern-card stats-card fade-in-up">
        <view class="card-header">
          <view class="header-icon">
            <text class="icon-text">🏆</text>
          </view>
          <text class="card-title">成就统计</text>
        </view>

        <view class="stats-grid">
          <view class="stat-item">
            <text class="stat-value unlocked">{{ unlockedAchievements }}</text>
            <text class="stat-label">已解锁成就</text>
          </view>
          <view class="stat-item">
            <text class="stat-value total">{{ totalAchievements }}</text>
            <text class="stat-label">总成就数</text>
          </view>
        </view>

        <view class="progress-section">
          <view class="progress-bar">
            <view class="progress-fill" :style="{ width: (unlockedAchievements / totalAchievements * 100) + '%' }"></view>
          </view>
          <text class="progress-text">完成度 {{ Math.round(unlockedAchievements / totalAchievements * 100) }}%</text>
        </view>
      </view>

      <!-- 累计打卡成就 -->
      <view class="modern-card category-card fade-in-up">
        <view class="category-header">
          <view class="category-icon">
            <text class="icon-text">📅</text>
          </view>
          <text class="category-title">累计打卡成就</text>
        </view>

        <scroll-view class="achievement-scroll" scroll-x enable-flex>
          <view class="achievement-item" v-for="(achievement, index) in cumulativePunchAchievements" :key="index">
            <view class="achievement-badge" :class="{ 'locked': !achievement.unlocked }">
              <text class="badge-icon">{{ achievement.icon }}</text>
            </view>
            <text class="achievement-title">{{ achievement.title }}</text>
            <text class="achievement-desc">{{ achievement.description }}</text>
            <view class="achievement-status" :class="{ 'unlocked': achievement.unlocked }">
              <text class="status-text">{{ achievement.unlocked ? '已解锁' : '未解锁' }}</text>
            </view>
          </view>
          <view class="no-achievements" v-if="cumulativePunchAchievements.length === 0">
            <view class="empty-icon">
              <text class="icon-text">🎯</text>
            </view>
            <text class="empty-text">暂无成就</text>
          </view>
        </scroll-view>
      </view>

      <!-- 连续打卡成就 -->
      <view class="modern-card category-card fade-in-up">
        <view class="category-header">
          <view class="category-icon">
            <text class="icon-text">🔥</text>
          </view>
          <text class="category-title">连续打卡成就</text>
        </view>

        <scroll-view class="achievement-scroll" scroll-x enable-flex>
          <view class="achievement-item" v-for="(achievement, index) in streakPunchAchievements" :key="index">
            <view class="achievement-badge" :class="{ 'locked': !achievement.unlocked }">
              <text class="badge-icon">{{ achievement.icon }}</text>
            </view>
            <text class="achievement-title">{{ achievement.title }}</text>
            <text class="achievement-desc">{{ achievement.description }}</text>
            <view class="achievement-status" :class="{ 'unlocked': achievement.unlocked }">
              <text class="status-text">{{ achievement.unlocked ? '已解锁' : '未解锁' }}</text>
            </view>
          </view>
          <view class="no-achievements" v-if="streakPunchAchievements.length === 0">
            <view class="empty-icon">
              <text class="icon-text">🎯</text>
            </view>
            <text class="empty-text">暂无成就</text>
          </view>
        </scroll-view>
      </view>

      <!-- 专注成就 -->
      <view class="modern-card category-card fade-in-up">
        <view class="category-header">
          <view class="category-icon">
            <text class="icon-text">🎯</text>
          </view>
          <text class="category-title">专注成就</text>
        </view>

        <scroll-view class="achievement-scroll" scroll-x enable-flex>
          <view class="achievement-item" v-for="(achievement, index) in focusAchievements" :key="index">
            <view class="achievement-badge" :class="{ 'locked': !achievement.unlocked }">
              <text class="badge-icon">{{ achievement.icon }}</text>
            </view>
            <text class="achievement-title">{{ achievement.title }}</text>
            <text class="achievement-desc">{{ achievement.description }}</text>
            <view class="achievement-status" :class="{ 'unlocked': achievement.unlocked }">
              <text class="status-text">{{ achievement.unlocked ? '已解锁' : '未解锁' }}</text>
            </view>
          </view>
          <view class="no-achievements" v-if="focusAchievements.length === 0">
            <view class="empty-icon">
              <text class="icon-text">🎯</text>
            </view>
            <text class="empty-text">暂无成就</text>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 底部装饰 -->
    <view class="bottom-decoration"></view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 成就数据（按分类存储）
const cumulativePunchAchievements = ref([]);
const streakPunchAchievements = ref([]);
const focusAchievements = ref([]);

// 加载并初始化成就数据
const loadData = () => {
  try {
    // 加载已有成就状态
    const storedAchievements = uni.getStorageSync('achievements') || '{}';
    const savedAchievements = JSON.parse(storedAchievements);

    // 加载 Monitor.vue 和 Dailypunch.vue 数据
    const storedMonitorRecords = uni.getStorageSync('monitorRecords') || '[]';
    const monitorRecords = JSON.parse(storedMonitorRecords);
    const storedPunchRecords = uni.getStorageSync('punchRecords') || '[]';
    const punchRecords = JSON.parse(storedPunchRecords);

    // 计算统计数据
    const monitorData = calculateMonitorData(monitorRecords);
    const punchData = calculatePunchData(punchRecords);

    // 定义成就（按分类）
    const achievementDefinitions = {
      cumulativePunch: [
        { title: '打卡初体验', icon: '🎉', description: '累计打卡1天', condition: punchData.totalPunchDays >= 1 },
        { title: '打卡入门', icon: '🏅', description: '累计打卡5天', condition: punchData.totalPunchDays >= 5 },
        { title: '打卡小成', icon: '🌟', description: '累计打卡10天', condition: punchData.totalPunchDays >= 10 },
        { title: '打卡达人', icon: '✅', description: '累计打卡30天', condition: punchData.totalPunchDays >= 30 },
        { title: '打卡大师', icon: '👑', description: '累计打卡100天', condition: punchData.totalPunchDays >= 100 },
      ],
      streakPunch: [
        { title: '坚持第一步', icon: '🚀', description: '连续打卡3天', condition: punchData.streakDays >= 3 },
        { title: '习惯养成', icon: '🌱', description: '连续打卡7天', condition: punchData.streakDays >= 7 },
        { title: '持之以恒', icon: '💪', description: '连续打卡14天', condition: punchData.streakDays >= 14 },
        { title: '不懈努力', icon: '🔥', description: '连续打卡30天', condition: punchData.streakDays >= 30 },
        { title: '毅力王者', icon: '🏆', description: '连续打卡60天', condition: punchData.streakDays >= 60 },
      ],
      focus: [
        { title: '专注新手', icon: '🕒', description: '累计专注30分钟', condition: monitorData.totalStudyTime >= 30 },
        { title: '专注能手', icon: '🎯', description: '累计专注2小时', condition: monitorData.totalStudyTime >= 120 },
        { title: '专注大师', icon: '✨', description: '累计专注5小时', condition: monitorData.totalStudyTime >= 300 },
        { title: '专注王者', icon: '🌟', description: '累计专注10小时', condition: monitorData.totalStudyTime >= 600 },
        { title: '专注大王', icon: '🛸', description: '累计专注15小时', condition: monitorData.totalStudyTime >= 900 },
      ]
    };

    // 初始化成就状态
    cumulativePunchAchievements.value = achievementDefinitions.cumulativePunch.map(achievement => ({
      ...achievement,
      unlocked: savedAchievements[achievement.title] || achievement.condition
    }));
    streakPunchAchievements.value = achievementDefinitions.streakPunch.map(achievement => ({
      ...achievement,
      unlocked: savedAchievements[achievement.title] || achievement.condition
    }));
    focusAchievements.value = achievementDefinitions.focus.map(achievement => ({
      ...achievement,
      unlocked: savedAchievements[achievement.title] || achievement.condition
    }));

    // 保存更新后的成就状态
    saveData();
  } catch (e) {
    console.error('加载成就数据失败', e);
    cumulativePunchAchievements.value = [];
    streakPunchAchievements.value = [];
    focusAchievements.value = [];
  }
};

// 保存成就数据
const saveData = () => {
  const achievementsState = {};
  cumulativePunchAchievements.value.forEach(achievement => {
    achievementsState[achievement.title] = achievement.unlocked;
  });
  streakPunchAchievements.value.forEach(achievement => {
    achievementsState[achievement.title] = achievement.unlocked;
  });
  focusAchievements.value.forEach(achievement => {
    achievementsState[achievement.title] = achievement.unlocked;
  });
  uni.setStorageSync('achievements', JSON.stringify(achievementsState));
};

// 计算 Monitor.vue 数据
const calculateMonitorData = (records) => {
  let totalStudyTime = 0;

  records.forEach(record => {
    const timeMatch = record.detail.match(/监测时长: (\d{2}):(\d{2}):(\d{2})/);
    if (timeMatch) {
      const hours = parseInt(timeMatch[1]);
      const minutes = parseInt(timeMatch[2]);
      const seconds = parseInt(timeMatch[3]);
      totalStudyTime += (hours * 60) + minutes + (seconds / 60); // 转换为分钟
    }
  });

  return { totalStudyTime: Math.round(totalStudyTime) };
};

// 计算 Dailypunch.vue 数据
const calculatePunchData = (records) => {
  const totalPunchDays = records.filter(record => record.punched).length;
  const sortedRecords = records.sort((a, b) => new Date(b.date) - new Date(a.date));
  let streakDays = 0;
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1);

  let currentDate = sortedRecords.some(r => r.date === formatDate(today) && r.punched)
    ? today
    : sortedRecords.some(r => r.date === formatDate(yesterday) && r.punched)
    ? yesterday
    : null;

  if (currentDate) {
    for (const record of sortedRecords) {
      const recordDate = new Date(record.date);
      if (record.punched && formatDate(recordDate) === formatDate(currentDate)) {
        streakDays++;
        currentDate.setDate(currentDate.getDate() - 1);
      } else if (recordDate < currentDate) {
        break;
      }
    }
  }

  return { totalPunchDays, streakDays };
};

// 格式化日期
const formatDate = (date) => {
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

// 统计数据
const unlockedAchievements = computed(() => {
  return (
    cumulativePunchAchievements.value.filter(a => a.unlocked).length +
    streakPunchAchievements.value.filter(a => a.unlocked).length +
    focusAchievements.value.filter(a => a.unlocked).length
  );
});
const totalAchievements = computed(() => {
  return (
    cumulativePunchAchievements.value.length +
    streakPunchAchievements.value.length +
    focusAchievements.value.length
  );
});

// 组件挂载时加载数据
onMounted(() => {
  loadData();
});
</script>

<style lang="scss">
// 使用全局现代化主题样式
.bottom-decoration {
  height: 100rpx;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.1), transparent);
  position: relative;
  z-index: 1;
}

// 统计卡片样式
.stats-card {
  .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;

    .header-icon {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      background: var(--achievement-gradient);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16rpx;

      .icon-text {
        font-size: 24rpx;
        color: white;
      }
    }

    .card-title {
      font-size: 28rpx;
      font-weight: 600;
      color: var(--gray-800);
    }
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24rpx;
    margin-bottom: 24rpx;

    .stat-item {
      text-align: center;

      .stat-value {
        display: block;
        font-size: 36rpx;
        font-weight: 700;
        margin-bottom: 8rpx;

        &.unlocked {
          color: var(--checkin-gradient);
        }

        &.total {
          color: var(--achievement-gradient);
        }
      }

      .stat-label {
        font-size: 22rpx;
        color: var(--gray-500);
        font-weight: 500;
      }
    }
  }

  .progress-section {
    .progress-bar {
      width: 100%;
      height: 12rpx;
      background: var(--gray-200);
      border-radius: 6rpx;
      overflow: hidden;
      margin-bottom: 12rpx;

      .progress-fill {
        height: 100%;
        background: var(--achievement-gradient);
        border-radius: 6rpx;
        transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
      }
    }

    .progress-text {
      font-size: 22rpx;
      color: var(--gray-600);
      text-align: center;
      font-weight: 500;
    }
  }
}

// 分类卡片样式
.category-card {
  margin-bottom: 24rpx;

  .category-header {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;

    .category-icon {
      width: 50rpx;
      height: 50rpx;
      border-radius: 50%;
      background: var(--plan-gradient);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16rpx;

      .icon-text {
        font-size: 20rpx;
        color: white;
      }
    }

    .category-title {
      font-size: 26rpx;
      font-weight: 600;
      color: var(--gray-800);
    }
  }

  .achievement-scroll {
    padding: 16rpx 0;

    .achievement-item {
      display: inline-flex;
      flex-direction: column;
      align-items: center;
      margin-right: 24rpx;
      width: 140rpx;
      padding: 16rpx;
      border-radius: var(--radius-lg);
      background: rgba(255, 255, 255, 0.5);
      transition: all 0.3s ease;

      &:active {
        transform: translateY(2rpx);
        background: rgba(255, 255, 255, 0.8);
      }

      .achievement-badge {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 12rpx;
        background: var(--achievement-gradient);
        transition: all 0.3s ease;

        &.locked {
          background: var(--gray-300);
          opacity: 0.6;
        }

        .badge-icon {
          font-size: 32rpx;
          color: white;
        }
      }

      .achievement-title {
        font-size: 22rpx;
        font-weight: 600;
        color: var(--gray-800);
        text-align: center;
        margin-bottom: 8rpx;
        line-height: 1.2;
      }

      .achievement-desc {
        font-size: 18rpx;
        color: var(--gray-500);
        text-align: center;
        margin-bottom: 12rpx;
        line-height: 1.3;
      }

      .achievement-status {
        padding: 4rpx 12rpx;
        border-radius: 12rpx;

        &.unlocked {
          background: var(--checkin-gradient);
        }

        &:not(.unlocked) {
          background: var(--gray-200);
        }

        .status-text {
          font-size: 18rpx;
          font-weight: 500;
          color: white;
        }

        &:not(.unlocked) .status-text {
          color: var(--gray-500);
        }
      }
    }

    .no-achievements {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      padding: 40rpx 20rpx;

      .empty-icon {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        background: var(--gray-200);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 16rpx;

        .icon-text {
          font-size: 40rpx;
          color: var(--gray-400);
        }
      }

      .empty-text {
        font-size: 24rpx;
        color: var(--gray-500);
        font-weight: 500;
      }
    }
  }
}
</style>