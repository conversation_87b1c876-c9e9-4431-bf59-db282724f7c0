<template>
  <view class="modern-page">
    <!-- 头部区域 -->
    <view class="modern-header">
      <text class="header-title">个人资料</text>
      <text class="header-subtitle">管理个人信息，完善学习档案</text>
      <view class="header-decoration"></view>
    </view>

    <!-- 内容区域 -->
    <view class="modern-content">
      <!-- 个人信息卡片 -->
      <view class="modern-card profile-card fade-in-up">
        <view class="card-header">
          <view class="header-icon">
            <text class="icon-text">👤</text>
          </view>
          <text class="card-title">个人信息</text>
        </view>

        <!-- 头像设置 -->
        <view class="avatar-section">
          <view class="avatar-wrapper" @click="chooseAvatar">
            <image class="avatar-image" :src="formData.avatar" mode="aspectFill" />
            <view class="avatar-overlay">
              <text class="overlay-text">📷</text>
            </view>
          </view>
          <text class="avatar-tip">点击更换头像</text>
        </view>

        <!-- 基本信息表单 -->
        <view class="form-section">
          <!-- 昵称 -->
          <view class="form-item">
            <text class="form-label">昵称</text>
            <input
              class="modern-input"
              v-model="formData.nickname"
              placeholder="请输入昵称"
            />
          </view>

          <!-- 国籍选择 -->
          <view class="form-item">
            <text class="form-label">国籍</text>
            <view class="country-selector">
              <!-- 搜索输入框 -->
              <input
                class="modern-input search-input"
                v-model="searchQuery"
                :placeholder="searchMode === 'zh' ? '用中文搜索国家' : 'Search countries in English'"
                @input="filterCountries"
              />

              <!-- 搜索模式切换 -->
              <view class="search-mode-toggle">
                <button
                  class="mode-btn"
                  :class="{ active: searchMode === 'zh' }"
                  @click="switchSearchMode('zh')"
                >
                  中文
                </button>
                <button
                  class="mode-btn"
                  :class="{ active: searchMode === 'en' }"
                  @click="switchSearchMode('en')"
                >
                  English
                </button>
              </view>

              <!-- 搜索结果列表 -->
              <scroll-view
                v-if="searchQuery && filteredCountries.length"
                class="search-results"
                scroll-y
              >
                <view
                  v-for="(country, index) in filteredCountries"
                  :key="index"
                  class="search-item"
                  @click="selectCountryFromSearch(country)"
                >
                  <text class="country-name">{{ country.name }}</text>
                </view>
              </scroll-view>

              <!-- 无结果提示 -->
              <view v-if="searchQuery && !filteredCountries.length" class="no-results">
                <text class="no-results-text">无匹配国家</text>
              </view>

              <!-- 国家选择器 -->
              <picker
                @change="onCountryChange"
                :value="countryIndex"
                :range="countries"
                range-key="name"
                class="country-picker"
              >
                <view class="picker-display">
                  <text class="selected-country">{{ countries[countryIndex].name }}</text>
                  <text class="picker-arrow">▼</text>
                </view>
              </picker>
            </view>
          </view>

          <!-- 手机号 -->
          <view class="form-item">
            <text class="form-label">手机号</text>
            <input
              class="modern-input"
              v-model="formData.phone"
              placeholder="请输入手机号"
              type="number"
            />
          </view>

          <!-- 邮箱 -->
          <view class="form-item">
            <text class="form-label">邮箱</text>
            <input
              class="modern-input"
              v-model="formData.email"
              placeholder="请输入邮箱"
              type="text"
            />
          </view>
        </view>

        <!-- 保存按钮 -->
        <button class="modern-button primary save-btn" @click="saveData">
          <text class="btn-icon">💾</text>
          <text class="btn-text">保存资料</text>
        </button>
      </view>
    </view>

    <!-- 底部装饰 -->
    <view class="bottom-decoration"></view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { apiRequest } from '@/utils/request.js';
import { migrateAllLocalData } from '@/utils/migrateLocalData.js';

// 表单数据
const formData = ref({
  avatar: '/common/images/1.png',
  nickname: '新用户',
  country: '中国🇨🇳',
  phone: '',
  email: ''
});

// 国家/地区列表（添加英文名称字段）
const countries = ref([
  { name: '中国🇨🇳', enName: 'China', code: 'CN' },
  { name: '阿布哈兹🇬🇪', enName: 'Abkhazia', code: 'GE' },
  { name: '阿富汗🇦🇫', enName: 'Afghanistan', code: 'AF' },
  { name: '阿尔巴尼亚🇦🇱', enName: 'Albania', code: 'AL' },
  { name: '阿尔及利亚🇩🇿', enName: 'Algeria', code: 'DZ' },
  { name: '安道尔🇦🇩', enName: 'Andorra', code: 'AD' },
  { name: '安哥拉🇦🇴', enName: 'Angola', code: 'AO' },
  { name: '安提瓜和巴布达🇦🇬', enName: 'Antigua and Barbuda', code: 'AG' },
  { name: '阿根廷🇦🇷', enName: 'Argentina', code: 'AR' },
  { name: '亚美尼亚🇦🇲', enName: 'Armenia', code: 'AM' },
  { name: '阿鲁巴🇦🇼', enName: 'Aruba', code: 'AW' },
  { name: '澳大利亚🇦🇺', enName: 'Australia', code: 'AU' },
  { name: '奥地利🇦🇹', enName: 'Austria', code: 'AT' },
  { name: '阿塞拜疆🇦🇿', enName: 'Azerbaijan', code: 'AZ' },
  { name: '巴哈马🇧🇸', enName: 'Bahamas', code: 'BS' },
  { name: '巴林🇧🇭', enName: 'Bahrain', code: 'BH' },
  { name: '孟加拉国🇧🇩', enName: 'Bangladesh', code: 'BD' },
  { name: '巴巴多斯🇧🇧', enName: 'Barbados', code: 'BB' },
  { name: '白俄罗斯🇧🇾', enName: 'Belarus', code: 'BY' },
  { name: '比利时🇧🇪', enName: 'Belgium', code: 'BE' },
  { name: '伯利兹🇧🇿', enName: 'Belize', code: 'BZ' },
  { name: '贝宁🇧🇯', enName: 'Benin', code: 'BJ' },
  { name: '百慕大🇧🇲', enName: 'Bermuda', code: 'BM' },
  { name: '不丹🇧🇹', enName: 'Bhutan', code: 'BT' },
  { name: '玻利维亚🇧🇴', enName: 'Bolivia', code: 'BO' },
  { name: '波斯尼亚和黑塞哥维那🇧🇦', enName: 'Bosnia and Herzegovina', code: 'BA' },
  { name: '博茨瓦纳🇧🇼', enName: 'Botswana', code: 'BW' },
  { name: '巴西🇧🇷', enName: 'Brazil', code: 'BR' },
  { name: '文莱🇧🇳', enName: 'Brunei', code: 'BN' },
  { name: '保加利亚🇧🇬', enName: 'Bulgaria', code: 'BG' },
  { name: '布基纳法索🇧🇫', enName: 'Burkina Faso', code: 'BF' },
  { name: '布隆迪🇧🇮', enName: 'Burundi', code: 'BI' },
  { name: '佛得角🇨🇻', enName: 'Cabo Verde', code: 'CV' },
  { name: '柬埔寨🇰🇭', enName: 'Cambodia', code: 'KH' },
  { name: '喀麦隆🇨🇲', enName: 'Cameroon', code: 'CM' },
  { name: '加拿大🇨🇦', enName: 'Canada', code: 'CA' },
  { name: '开曼群岛🇰🇾', enName: 'Cayman Islands', code: 'KY' },
  { name: '中非共和国🇨🇫', enName: 'Central African Republic', code: 'CF' },
  { name: '乍得🇹🇩', enName: 'Chad', code: 'TD' },
  { name: '智利🇨🇱', enName: 'Chile', code: 'CL' },
  { name: '哥伦比亚🇨🇴', enName: 'Colombia', code: 'CO' },
  { name: '科摩罗🇰🇲', enName: 'Comoros', code: 'KM' },
  { name: '刚果（布）🇨🇬', enName: 'Congo (Brazzaville)', code: 'CG' },
  { name: '刚果（金）🇨🇩', enName: 'Congo (Kinshasa)', code: 'CD' },
  { name: '库克群岛🇨🇰', enName: 'Cook Islands', code: 'CK' },
  { name: '哥斯达黎加🇨🇷', enName: 'Costa Rica', code: 'CR' },
  { name: '科特迪瓦🇨🇮', enName: "Côte d'Ivoire", code: 'CI' },
  { name: '克罗地亚🇭🇷', enName: 'Croatia', code: 'HR' },
  { name: '古巴🇨🇺', enName: 'Cuba', code: 'CU' },
  { name: '库拉索🇨🇼', enName: 'Curaçao', code: 'CW' },
  { name: '塞浦路斯🇨🇾', enName: 'Cyprus', code: 'CY' },
  { name: '捷克共和国🇨🇿', enName: 'Czech Republic', code: 'CZ' },
  { name: '丹麦🇩🇰', enName: 'Denmark', code: 'DK' },
  { name: '吉布提🇩🇯', enName: 'Djibouti', code: 'DJ' },
  { name: '多米尼克🇩🇲', enName: 'Dominica', code: 'DM' },
  { name: '多米尼加共和国🇩🇴', enName: 'Dominican Republic', code: 'DO' },
  { name: '厄瓜多尔🇪🇨', enName: 'Ecuador', code: 'EC' },
  { name: '埃及🇪🇬', enName: 'Egypt', code: 'EG' },
  { name: '萨尔瓦多🇸🇻', enName: 'El Salvador', code: 'SV' },
  { name: '赤道几内亚🇬🇶', enName: 'Equatorial Guinea', code: 'GQ' },
  { name: '厄立特里亚🇪🇷', enName: 'Eritrea', code: 'ER' },
  { name: '爱沙尼亚🇪🇪', enName: 'Estonia', code: 'EE' },
  { name: '埃斯瓦蒂尼🇸🇿', enName: 'Eswatini', code: 'SZ' },
  { name: '埃塞俄比亚🇪🇹', enName: 'Ethiopia', code: 'ET' },
  { name: '斐济🇫🇯', enName: 'Fiji', code: 'FJ' },
  { name: '芬兰🇫🇮', enName: 'Finland', code: 'FI' },
  { name: '法国🇫🇷', enName: 'France', code: 'FR' },
  { name: '加蓬🇬🇦', enName: 'Gabon', code: 'GA' },
  { name: '冈比亚🇬🇲', enName: 'Gambia', code: 'GM' },
  { name: '格鲁吉亚🇬🇪', enName: 'Georgia', code: 'GE' },
  { name: '德国🇩🇪', enName: 'Germany', code: 'DE' },
  { name: '加纳🇬🇭', enName: 'Ghana', code: 'GH' },
  { name: '希腊🇬🇷', enName: 'Greece', code: 'GR' },
  { name: '格陵兰🇬🇱', enName: 'Greenland', code: 'GL' },
  { name: '格林纳达🇬🇽', enName: 'Grenada', code: 'GD' },
  { name: '关岛🇬🇺', enName: 'Guam', code: 'GU' },
  { name: '危地马拉🇬🇹', enName: 'Guatemala', code: 'GT' },
  { name: '几内亚🇬🇳', enName: 'Guinea', code: 'GN' },
  { name: '几内亚比绍🇬🇼', enName: 'Guinea-Bissau', code: 'GW' },
  { name: '圭亚那🇬🇾', enName: 'Guyana', code: 'GY' },
  { name: '海地🇭🇹', enName: 'Haiti', code: 'HT' },
  { name: '洪都拉斯🇭🇳', enName: 'Honduras', code: 'HN' },
  { name: '香港🇭🇰', enName: 'Hong Kong', code: 'HK' },
  { name: '匈牙利🇭🇺', enName: 'Hungary', code: 'HU' },
  { name: '冰岛🇮🇸', enName: 'Iceland', code: 'IS' },
  { name: '印度🇮🇳', enName: 'India', code: 'IN' },
  { name: '印度尼西亚🇮🇩', enName: 'Indonesia', code: 'ID' },
  { name: '伊朗🇮🇷', enName: 'Iran', code: 'IR' },
  { name: '伊拉克🇮🇶', enName: 'Iraq', code: 'IQ' },
  { name: '爱尔兰🇮🇪', enName: 'Ireland', code: 'IE' },
  { name: '以色列🇮🇱', enName: 'Israel', code: 'IL' },
  { name: '意大利🇮🇹', enName: 'Italy', code: 'IT' },
  { name: '牙买加🇯🇲', enName: 'Jamaica', code: 'JM' },
  { name: '日本🇯🇵', enName: 'Japan', code: 'JP' },
  { name: '约旦🇯🇴', enName: 'Jordan', code: 'JO' },
  { name: '哈萨克斯坦🇰🇿', enName: 'Kazakhstan', code: 'KZ' },
  { name: '肯尼亚🇰🇪', enName: 'Kenya', code: 'KE' },
  { name: '基里巴斯🇰🇮', enName: 'Kiribati', code: 'KI' },
  { name: '科索沃🇽🇰', enName: 'Kosovo', code: 'XK' },
  { name: '科威特🇰🇼', enName: 'Kuwait', code: 'KW' },
  { name: '吉尔吉斯斯坦🇰🇬', enName: 'Kyrgyzstan', code: 'KG' },
  { name: '老挝🇱🇦', enName: 'Laos', code: 'LA' },
  { name: '拉脱维亚🇱🇻', enName: 'Latvia', code: 'LV' },
  { name: '黎巴嫩🇱🇧', enName: 'Lebanon', code: 'LB' },
  { name: '莱索托🇱🇸', enName: 'Lesotho', code: 'LS' },
  { name: '利比里亚🇱🇷', enName: 'Liberia', code: 'LR' },
  { name: '利比亚🇱🇾', enName: 'Libya', code: 'LY' },
  { name: '列支敦士登🇱🇮', enName: 'Liechtenstein', code: 'LI' },
  { name: '立陶宛🇱🇹', enName: 'Lithuania', code: 'LT' },
  { name: '卢森堡🇱🇺', enName: 'Luxembourg', code: 'LU' },
  { name: '澳门🇲🇴', enName: 'Macao', code: 'MO' },
  { name: '马达加斯加🇲🇬', enName: 'Madagascar', code: 'MG' },
  { name: '马拉维🇲🇼', enName: 'Malawi', code: 'MW' },
  { name: '马来西亚🇲🇾', enName: 'Malaysia', code: 'MY' },
  { name: '马尔代夫🇲🇻', enName: 'Maldives', code: 'MV' },
  { name: '马里🇲🇱', enName: 'Mali', code: 'ML' },
  { name: '马耳他🇲🇹', enName: 'Malta', code: 'MT' },
  { name: '马绍尔群岛🇲🇭', enName: 'Marshall Islands', code: 'MH' },
  { name: '毛里塔尼亚🇲🇷', enName: 'Mauritania', code: 'MR' },
  { name: '毛里求斯🇲🇺', enName: 'Mauritius', code: 'MU' },
  { name: '墨西哥🇲🇽', enName: 'Mexico', code: 'MX' },
  { name: '密克罗尼西亚🇫🇲', enName: 'Micronesia', code: 'FM' },
  { name: '摩尔多瓦🇲🇩', enName: 'Moldova', code: 'MD' },
  { name: '摩纳哥🇲🇨', enName: 'Monaco', code: 'MC' },
  { name: '蒙古🇲🇳', enName: 'Mongolia', code: 'MN' },
  { name: '黑山🇲🇪', enName: 'Montenegro', code: 'ME' },
  { name: '摩洛哥🇲🇦', enName: 'Morocco', code: 'MA' },
  { name: '莫桑比克🇲🇿', enName: 'Mozambique', code: 'MZ' },
  { name: '缅甸🇲🇲', enName: 'Myanmar', code: 'MM' },
  { name: '纳米比亚🇳🇦', enName: 'Namibia', code: 'NA' },
  { name: '瑙鲁🇳🇷', enName: 'Nauru', code: 'NR' },
  { name: '尼泊尔🇳🇵', enName: 'Nepal', code: 'NP' },
  { name: '荷兰🇳🇱', enName: 'Netherlands', code: 'NL' },
  { name: '新西兰🇳🇿', enName: 'New Zealand', code: 'NZ' },
  { name: '尼加拉瓜🇳🇮', enName: 'Nicaragua', code: 'NI' },
  { name: '尼日尔🇳🇪', enName: 'Niger', code: 'NE' },
  { name: '尼日利亚🇳🇬', enName: 'Nigeria', code: 'NG' },
  { name: '纽埃🇳🇺', enName: 'Niue', code: 'NU' },
  { name: '北马其顿🇲🇰', enName: 'North Macedonia', code: 'MK' },
  { name: '北塞浦路斯🇹🇷', enName: 'Northern Cyprus', code: 'TR' },
  { name: '挪威🇳🇴', enName: 'Norway', code: 'NO' },
  { name: '阿曼🇴🇲', enName: 'Oman', code: 'OM' },
  { name: '巴基斯坦🇵🇰', enName: 'Pakistan', code: 'PK' },
  { name: '帕劳🇵🇼', enName: 'Palau', code: 'PW' },
  { name: '巴勒斯坦🇵🇸', enName: 'Palestine', code: 'PS' },
  { name: '巴拿马🇵🇦', enName: 'Panama', code: 'PA' },
  { name: '巴布亚新几内亚🇵🇬', enName: 'Papua New Guinea', code: 'PG' },
  { name: '巴拉圭🇵🇾', enName: 'Paraguay', code: 'PY' },
  { name: '秘鲁🇵🇪', enName: 'Peru', code: 'PE' },
  { name: '菲律宾🇵🇭', enName: 'Philippines', code: 'PH' },
  { name: '波兰🇵🇱', enName: 'Poland', code: 'PL' },
  { name: '葡萄牙🇵🇹', enName: 'Portugal', code: 'PT' },
  { name: '波多黎各🇵🇷', enName: 'Puerto Rico', code: 'PR' },
  { name: '卡塔尔🇶🇦', enName: 'Qatar', code: 'QA' },
  { name: '罗马尼亚🇷🇴', enName: 'Romania', code: 'RO' },
  { name: '俄罗斯🇷🇺', enName: 'Russia', code: 'RU' },
  { name: '卢旺达🇷🇼', enName: 'Rwanda', code: 'RW' },
  { name: '圣基茨和尼维斯🇰🇳', enName: 'Saint Kitts and Nevis', code: 'KN' },
  { name: '圣卢西亚🇱🇨', enName: 'Saint Lucia', code: 'LC' },
  { name: '圣文森特和格林纳丁斯🇻🇨', enName: 'Saint Vincent and the Grenadines', code: 'VC' },
  { name: '萨摩亚🇼🇸', enName: 'Samoa', code: 'WS' },
  { name: '圣马力诺🇸🇲', enName: 'San Marino', code: 'SM' },
  { name: '圣多美和普林西比🇸🇹', enName: 'São Tomé and Príncipe', code: 'ST' },
  { name: '沙特阿拉伯🇸🇦', enName: 'Saudi Arabia', code: 'SA' },
  { name: '塞内加尔🇸🇳', enName: 'Senegal', code: 'SN' },
  { name: '塞尔维亚🇷🇸', enName: 'Serbia', code: 'RS' },
  { name: '塞舌尔🇸🇨', enName: 'Seychelles', code: 'SC' },
  { name: '塞拉利昂🇸🇱', enName: 'Sierra Leone', code: 'SL' },
  { name: '新加坡🇸🇬', enName: 'Singapore', code: 'SG' },
  { name: '圣马丁（荷兰部分）🇸🇽', enName: 'Sint Maarten', code: 'SX' },
  { name: '斯洛伐克🇸🇰', enName: 'Slovakia', code: 'SK' },
  { name: '斯洛文尼亚🇸🇮', enName: 'Slovenia', code: 'SI' },
  { name: '所罗门群岛🇸🇧', enName: 'Solomon Islands', code: 'SB' },
  { name: '索马里🇸🇴', enName: 'Somalia', code: 'SO' },
  { name: '索马里兰🇸🇴', enName: 'Somaliland', code: 'SO' },
  { name: '南非🇿🇦', enName: 'South Africa', code: 'ZA' },
  { name: '南奥塞梯🇬🇪', enName: 'South Ossetia', code: 'GE' },
  { name: '南苏丹🇸🇸', enName: 'South Sudan', code: 'SS' },
  { name: '西班牙🇪🇸', enName: 'Spain', code: 'ES' },
  { name: '斯里兰卡🇱🇰', enName: 'Sri Lanka', code: 'LK' },
  { name: '苏丹🇸🇩', enName: 'Sudan', code: 'SD' },
  { name: '苏里南🇸🇷', enName: 'Suriname', code: 'SR' },
  { name: '瑞典🇸🇪', enName: 'Sweden', code: 'SE' },
  { name: '瑞士🇨🇭', enName: 'Switzerland', code: 'CH' },
  { name: '叙利亚🇸🇾', enName: 'Syria', code: 'SY' },
  { name: '台湾🇹🇼', enName: 'Taiwan', code: 'TW' },
  { name: '塔吉克斯坦🇹🇯', enName: 'Tajikistan', code: 'TJ' },
  { name: '坦桑尼亚🇹🇿', enName: 'Tanzania', code: 'TZ' },
  { name: '泰国🇹🇭', enName: 'Thailand', code: 'TH' },
  { name: '东帝汶🇹🇱', enName: 'Timor-Leste', code: 'TL' },
  { name: '多哥🇹🇬', enName: 'Togo', code: 'TG' },
  { name: '托克劳🇹🇰', enName: 'Tokelau', code: 'TK' },
  { name: '汤加🇹🇴', enName: 'Tonga', code: 'TO' },
  { name: '特立尼达和多巴哥🇹🇹', enName: 'Trinidad and Tobago', code: 'TT' },
  { name: '突尼斯🇹🇳', enName: 'Tunisia', code: 'TN' },
  { name: '土耳其🇹🇷', enName: 'Turkey', code: 'TR' },
  { name: '土库曼斯坦🇹🇲', enName: 'Turkmenistan', code: 'TM' },
  { name: '图瓦卢🇹🇻', enName: 'Tuvalu', code: 'TV' },
  { name: '乌干达🇺🇬', enName: 'Uganda', code: 'UG' },
  { name: '乌克兰🇺🇦', enName: 'Ukraine', code: 'UA' },
  { name: '阿联酋🇦🇪', enName: 'United Arab Emirates', code: 'AE' },
  { name: '英国🇬🇧', enName: 'United Kingdom', code: 'GB' },
  { name: '美国🇺🇸', enName: 'United States', code: 'US' },
  { name: '乌拉圭🇺🇾', enName: 'Uruguay', code: 'UY' },
  { name: '乌兹别克斯坦🇺🇿', enName: 'Uzbekistan', code: 'UZ' },
  { name: '瓦努阿图🇻🇺', enName: 'Vanuatu', code: 'VU' },
  { name: '梵蒂冈🇻🇦', enName: 'Vatican City', code: 'VA' },
  { name: '委内瑞拉🇻🇪', enName: 'Venezuela', code: 'VE' },
  { name: '越南🇻🇳', enName: 'Vietnam', code: 'VN' },
  { name: '也门🇾🇪', enName: 'Yemen', code: 'YE' },
  { name: '赞比亚🇿🇲', enName: 'Zambia', code: 'ZM' },
  { name: '津巴布韦🇿🇼', enName: 'Zimbabwe', code: 'ZW' }
]);

const countryIndex = ref(0);
const searchQuery = ref('');
const filteredCountries = ref([]);
const searchMode = ref('zh'); // 默认中文搜索模式

// 页面加载时初始化数据
onMounted(async () => {
  await migrateAllLocalData();
  // 获取后端资料
  try {
    const res = await apiRequest({ url: '/api/profile', method: 'GET' });
    Object.assign(formData.value, res);
  } catch (e) {}
});

// 选择头像
const chooseAvatar = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['original', 'compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      formData.value.avatar = res.tempFilePaths[0];
    }
  });
};

// picker 选择国家
const onCountryChange = (e) => {
  countryIndex.value = e.detail.value;
  formData.value.country = countries.value[countryIndex.value].name;
  searchQuery.value = ''; // 清空搜索框
  filteredCountries.value = countries.value; // 重置过滤列表
};

// 切换搜索模式
const switchSearchMode = (mode) => {
  searchMode.value = mode;
  searchQuery.value = ''; // 清空搜索框
  filteredCountries.value = countries.value; // 重置过滤列表
};

// 过滤国家列表
const filterCountries = () => {
  const query = searchQuery.value.trim().toLowerCase();
  if (!query) {
    filteredCountries.value = countries.value;
    return;
  }
  filteredCountries.value = countries.value.filter(country => {
    if (searchMode.value === 'zh') {
      return country.name.toLowerCase().includes(query);
    } else {
      return country.enName.toLowerCase().includes(query);
    }
  });
};

// 从搜索结果中选择国家
const selectCountryFromSearch = (country) => {
  formData.value.country = country.name;
  const index = countries.value.findIndex(item => item.name === country.name);
  countryIndex.value = index !== -1 ? index : 0;
  searchQuery.value = ''; // 清空搜索框
  filteredCountries.value = countries.value; // 重置过滤列表
};

// 保存数据
const saveData = () => {
  const userData = {
    avatar: formData.value.avatar,
    username: formData.value.username,
    country: formData.value.country,
    phone: formData.value.phone,
    email: formData.value.email
  };
  uni.setStorageSync('userData', userData);

  uni.showToast({
    title: '保存成功',
    icon: 'success',
    success: () => {
      setTimeout(() => {
        uni.reLaunch({
          url: '/pages/enter/enter'
        });
      }, 1000);
    }
  });
};
</script>

<style lang="scss">
// 使用全局现代化主题样式
.bottom-decoration {
  height: 100rpx;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.1), transparent);
  position: relative;
  z-index: 1;
}

// 个人信息卡片样式
.profile-card {
  .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 32rpx;

    .header-icon {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      background: var(--profile-gradient);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16rpx;

      .icon-text {
        font-size: 24rpx;
        color: white;
      }
    }

    .card-title {
      font-size: 28rpx;
      font-weight: 600;
      color: var(--gray-800);
    }
  }

  .avatar-section {
    text-align: center;
    margin-bottom: 32rpx;

    .avatar-wrapper {
      position: relative;
      width: 120rpx;
      height: 120rpx;
      margin: 0 auto 16rpx;
      border-radius: 50%;
      overflow: hidden;
      cursor: pointer;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.95);
      }

      .avatar-image {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        border: 4rpx solid rgba(255, 255, 255, 0.8);
        box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
      }

      .avatar-overlay {
        position: absolute;
        inset: 0;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;

        .overlay-text {
          font-size: 32rpx;
          color: white;
        }
      }

      &:hover .avatar-overlay {
        opacity: 1;
      }
    }

    .avatar-tip {
      font-size: 22rpx;
      color: var(--gray-500);
    }
  }

  .form-section {
    margin-bottom: 32rpx;

    .form-item {
      margin-bottom: 24rpx;

      .form-label {
        display: block;
        font-size: 24rpx;
        font-weight: 600;
        color: var(--gray-700);
        margin-bottom: 8rpx;
      }

      .modern-input {
        width: 100%;
        height: 80rpx;
        border: 2rpx solid var(--gray-200);
        border-radius: var(--radius-lg);
        padding: 0 24rpx;
        font-size: 28rpx;
        color: var(--gray-800);
        background: var(--white);
        transition: all 0.3s ease;

        &:focus {
          border-color: var(--profile-gradient);
          box-shadow: 0 0 0 4rpx rgba(139, 92, 246, 0.1);
        }

        &::placeholder {
          color: var(--gray-400);
        }
      }

      .country-selector {
        .search-input {
          margin-bottom: 16rpx;
        }

        .search-mode-toggle {
          display: flex;
          gap: 8rpx;
          margin-bottom: 16rpx;

          .mode-btn {
            flex: 1;
            height: 60rpx;
            border: 2rpx solid var(--gray-200);
            border-radius: var(--radius-lg);
            background: var(--white);
            color: var(--gray-600);
            font-size: 24rpx;
            font-weight: 500;
            transition: all 0.3s ease;

            &.active {
              border-color: var(--profile-gradient);
              background: var(--profile-gradient);
              color: white;
            }

            &:not(.active):active {
              background: var(--gray-50);
            }
          }
        }
        .search-results {
          max-height: 300rpx;
          background: var(--white);
          border: 2rpx solid var(--gray-200);
          border-radius: var(--radius-lg);
          position: absolute;
          top: 180rpx;
          left: 0;
          right: 0;
          z-index: 10;
          box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

          .search-item {
            padding: 16rpx 20rpx;
            border-bottom: 1rpx solid var(--gray-100);
            transition: background 0.3s ease;

            &:last-child {
              border-bottom: none;
            }

            &:active {
              background: var(--gray-50);
            }

            .country-name {
              font-size: 26rpx;
              color: var(--gray-800);
            }
          }
        }

        .no-results {
          padding: 20rpx;
          background: var(--white);
          border: 2rpx solid var(--gray-200);
          border-radius: var(--radius-lg);
          position: absolute;
          top: 180rpx;
          left: 0;
          right: 0;
          z-index: 10;
          text-align: center;

          .no-results-text {
            font-size: 24rpx;
            color: var(--gray-500);
          }
        }

        .country-picker {
          .picker-display {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 80rpx;
            border: 2rpx solid var(--gray-200);
            border-radius: var(--radius-lg);
            padding: 0 24rpx;
            background: var(--white);
            transition: all 0.3s ease;

            &:active {
              border-color: var(--profile-gradient);
            }

            .selected-country {
              font-size: 26rpx;
              color: var(--gray-800);
              flex: 1;
            }

            .picker-arrow {
              font-size: 20rpx;
              color: var(--gray-500);
            }
          }
        }
      }
    }
  }

  .save-btn {
    width: 100%;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8rpx;

    .btn-icon {
      font-size: 20rpx;
    }

    .btn-text {
      font-size: 28rpx;
      font-weight: 500;
    }
  }
}
</style>