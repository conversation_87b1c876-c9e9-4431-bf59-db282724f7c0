/**
 * 应用配置文件
 * 用于管理API密钥和其他配置项
 */

// 配置项键名
const CONFIG_KEYS = {
  GEMINI_API_KEY: 'gemini_api_key',
  USE_AI_RECOGNITION: 'use_ai_recognition'
};

/**
 * 获取配置项
 * @param {String} key - 配置项键名
 * @param {*} defaultValue - 默认值
 * @returns {*} 配置值
 */
export function getConfig(key, defaultValue = null) {
  try {
    const value = uni.getStorageSync(key);
    return value !== '' ? value : defaultValue;
  } catch (error) {
    console.error('获取配置失败:', error);
    return defaultValue;
  }
}

/**
 * 设置配置项
 * @param {String} key - 配置项键名
 * @param {*} value - 配置值
 * @returns {Boolean} 是否设置成功
 */
export function setConfig(key, value) {
  try {
    uni.setStorageSync(key, value);
    return true;
  } catch (error) {
    console.error('设置配置失败:', error);
    return false;
  }
}

/**
 * 获取Gemini API密钥
 * @returns {String} API密钥
 */
export function getGeminiApiKey() {
  return getConfig(CONFIG_KEYS.GEMINI_API_KEY, '');
}

/**
 * 设置Gemini API密钥
 * @param {String} apiKey - API密钥
 * @returns {Boolean} 是否设置成功
 */
export function setGeminiApiKey(apiKey) {
  return setConfig(CONFIG_KEYS.GEMINI_API_KEY, apiKey);
}

/**
 * 检查Gemini API密钥是否已配置
 * @returns {Boolean} 是否已配置
 */
export function isGeminiApiKeyConfigured() {
  const apiKey = getGeminiApiKey();
  return apiKey && apiKey.trim() !== '' && apiKey !== 'YOUR_GEMINI_API_KEY';
}

/**
 * 获取AI识别开关状态
 * @returns {Boolean} 是否启用AI识别
 */
export function getUseAIRecognition() {
  return getConfig(CONFIG_KEYS.USE_AI_RECOGNITION, true);
}

/**
 * 设置AI识别开关状态
 * @param {Boolean} useAI - 是否启用AI识别
 * @returns {Boolean} 是否设置成功
 */
export function setUseAIRecognition(useAI) {
  return setConfig(CONFIG_KEYS.USE_AI_RECOGNITION, useAI);
}

/**
 * 显示API密钥配置对话框
 * @returns {Promise<String>} 用户输入的API密钥
 */
export function showApiKeyConfigDialog() {
  return new Promise((resolve, reject) => {
    const currentKey = getGeminiApiKey();
    
    uni.showModal({
      title: '配置Gemini API密钥',
      content: '请输入您的Gemini API密钥以启用AI识别功能',
      editable: true,
      placeholderText: '请输入API密钥',
      success: (res) => {
        if (res.confirm && res.content && res.content.trim()) {
          const apiKey = res.content.trim();
          if (setGeminiApiKey(apiKey)) {
            uni.showToast({
              title: 'API密钥配置成功',
              icon: 'success'
            });
            resolve(apiKey);
          } else {
            uni.showToast({
              title: '配置失败，请重试',
              icon: 'none'
            });
            reject(new Error('配置失败'));
          }
        } else {
          reject(new Error('用户取消或输入为空'));
        }
      },
      fail: (error) => {
        reject(error);
      }
    });
  });
}

/**
 * 获取Gemini API申请指南
 * @returns {String} 申请指南文本
 */
export function getGeminiApiGuide() {
  return `获取免费Gemini API密钥步骤：

1. 访问 Google AI Studio
   网址：https://aistudio.google.com/

2. 使用Google账号登录

3. 点击"Get API key"按钮

4. 创建新的API密钥

5. 复制生成的API密钥

6. 在应用中粘贴API密钥

注意：
- Gemini API提供免费额度
- 请妥善保管您的API密钥
- 不要在公共场所分享API密钥`;
}

export { CONFIG_KEYS };
