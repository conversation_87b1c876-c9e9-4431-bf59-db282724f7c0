.bottom-decoration{height:3.125rem;background:linear-gradient(to top,rgba(255,255,255,.1),transparent);position:relative;z-index:1}.search-card{margin-bottom:.75rem}.search-card .card-header{display:flex;align-items:center;margin-bottom:.75rem}.search-card .card-header .header-icon{width:1.875rem;height:1.875rem;border-radius:50%;background:var(--profile-gradient);display:flex;align-items:center;justify-content:center;margin-right:.5rem}.search-card .card-header .header-icon .icon-text{font-size:.75rem;color:#fff}.search-card .card-header .card-title{font-size:.875rem;font-weight:600;color:var(--gray-800)}.search-card .search-form .modern-input{width:100%;height:2.5rem;border:.0625rem solid var(--gray-200);border-radius:var(--radius-lg);padding:0 .75rem;font-size:.875rem;color:var(--gray-800);background:var(--white);margin-bottom:.5rem;transition:all .3s ease}.search-card .search-form .modern-input:focus{border-color:var(--profile-gradient);box-shadow:0 0 0 .125rem rgba(139,92,246,.1)}.search-card .search-form .modern-input::-webkit-input-placeholder{color:var(--gray-400)}.search-card .search-form .modern-input::placeholder{color:var(--gray-400)}.search-card .search-form .search-mode-toggle{display:flex;gap:.25rem}.search-card .search-form .search-mode-toggle .mode-btn{flex:1;height:1.875rem;border:.0625rem solid var(--gray-200);border-radius:var(--radius-lg);background:var(--white);color:var(--gray-600);font-size:.75rem;font-weight:500;transition:all .3s ease}.search-card .search-form .search-mode-toggle .mode-btn.active{border-color:var(--profile-gradient);background:var(--profile-gradient);color:#fff}.search-card .search-form .search-mode-toggle .mode-btn:not(.active):active{background:var(--gray-50)}.regions-card{flex:1;display:flex;flex-direction:column}.regions-card .card-header{display:flex;align-items:center;justify-content:space-between;margin-bottom:.625rem}.regions-card .card-header .header-icon{width:1.875rem;height:1.875rem;border-radius:50%;background:var(--profile-gradient);display:flex;align-items:center;justify-content:center}.regions-card .card-header .header-icon .icon-text{font-size:.75rem;color:#fff}.regions-card .card-header .card-title{font-size:.875rem;font-weight:600;color:var(--gray-800);flex:1;margin-left:.5rem}.regions-card .card-header .card-subtitle{font-size:.6875rem;color:var(--gray-500)}.regions-card .regions-list{flex:1}.regions-card .regions-list .region-item{display:flex;align-items:center;padding:.625rem 0;border-bottom:.03125rem solid var(--gray-200);transition:all .3s ease}.regions-card .regions-list .region-item:last-child{border-bottom:none}.regions-card .regions-list .region-item:active{background:rgba(255,255,255,.5);border-radius:var(--radius-lg)}.regions-card .regions-list .region-item .region-flag{width:1.875rem;height:1.875rem;border-radius:50%;background:var(--gray-100);display:flex;align-items:center;justify-content:center;margin-right:.5rem}.regions-card .regions-list .region-item .region-flag .flag-text{font-size:1rem}.regions-card .regions-list .region-item .region-info{flex:1}.regions-card .regions-list .region-item .region-info .region-name{font-size:.8125rem;font-weight:600;color:var(--gray-800);margin-bottom:.125rem;display:block}.regions-card .regions-list .region-item .region-info .region-details{font-size:.6875rem;color:var(--gray-500);display:block}.regions-card .regions-list .region-item .select-arrow{font-size:1rem;color:var(--gray-400);font-weight:300}
