<template>
  <view class="modern-page">
    <!-- 头部区域 -->
    <view class="modern-header">
      <text class="header-title">应用设置</text>
      <text class="header-subtitle">个性化设置，打造专属学习环境</text>
      <view class="header-decoration"></view>
    </view>

    <!-- 内容区域 -->
    <view class="modern-content">
      <!-- 设置卡片 -->
      <view class="modern-card settings-card fade-in-up">
        <view class="card-header">
          <view class="header-icon">
            <text class="icon-text">⚙️</text>
          </view>
          <text class="card-title">应用设置</text>
        </view>

        <view class="settings-list">
          <!-- 通知设置 -->
          <view class="setting-item">
            <view class="setting-info">
              <view class="setting-icon notification">
                <text class="icon-text">🔔</text>
              </view>
              <view class="setting-content">
                <text class="setting-label">通知提醒</text>
                <text class="setting-desc">接收学习提醒和重要通知</text>
              </view>
            </view>
            <switch
              class="modern-switch"
              :checked="notificationEnabled"
              @change="toggleNotification"
            />
          </view>

          <!-- 检查更新 -->
          <view class="setting-item clickable" @click="checkUpdate">
            <view class="setting-info">
              <view class="setting-icon update">
                <text class="icon-text">🔄</text>
              </view>
              <view class="setting-content">
                <text class="setting-label">检查更新</text>
                <text class="setting-desc">当前版本：1.0.3</text>
              </view>
            </view>
            <text class="setting-arrow">›</text>
          </view>

          <!-- 评价与反馈 -->
          <view class="setting-item clickable" @click="goToFeedback">
            <view class="setting-info">
              <view class="setting-icon feedback">
                <text class="icon-text">💬</text>
              </view>
              <view class="setting-content">
                <text class="setting-label">评价与反馈</text>
                <text class="setting-desc">给我们提出建议</text>
              </view>
            </view>
            <text class="setting-arrow">›</text>
          </view>

          <!-- 重置数据 -->
          <view class="setting-item danger clickable" @click="resetData">
            <view class="setting-info">
              <view class="setting-icon reset">
                <text class="icon-text">🗑️</text>
              </view>
              <view class="setting-content">
                <text class="setting-label danger-text">重置数据</text>
                <text class="setting-desc danger-desc">将清除所有数据，请谨慎操作</text>
              </view>
            </view>
            <text class="setting-arrow danger-arrow">›</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部装饰 -->
    <view class="bottom-decoration"></view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 通知开关状态
const notificationEnabled = ref(true);

// 加载通知开关状态
const loadNotificationSetting = () => {
  try {
    const storedSetting = uni.getStorageSync('notificationEnabled');
    if (storedSetting !== '') {
      notificationEnabled.value = storedSetting === 'true';
    } else {
      notificationEnabled.value = true;
      saveNotificationSetting();
    }
  } catch (e) {
    console.error('加载通知设置失败', e);
    notificationEnabled.value = true;
    saveNotificationSetting();
  }
};

// 保存通知开关状态
const saveNotificationSetting = () => {
  try {
    uni.setStorageSync('notificationEnabled', notificationEnabled.value.toString());
  } catch (e) {
    console.error('保存通知设置失败', e);
  }
};

// 切换通知开关
const toggleNotification = (e) => {
  notificationEnabled.value = e.detail.value;
  saveNotificationSetting();
  uni.showToast({ title: `通知已${notificationEnabled.value ? '开启' : '关闭'}`, icon: 'success' });
};

// 检查更新
const checkUpdate = () => {
  uni.showLoading({ title: '检查更新中...' });
  setTimeout(() => {
    uni.hideLoading();
    uni.showModal({
      title: '检查更新',
      content: '当前已是最新版本：1.0.3',
      showCancel: false,
      confirmText: '确定',
    });
  }, 1000);
};

// 新增导航到反馈页面
const goToFeedback = () => {
  uni.navigateTo({
    url: '/pages/helpfeedback/helpfeedback'
  });
};

// 重置数据
const resetData = () => {
  uni.showModal({
    title: '重置数据',
    content: '此操作将清除所有数据（包括用户信息、好友列表、动态等），是否继续？',
    confirmText: '确定',
    cancelText: '取消',
    success: (res) => {
      if (res.confirm) {
        uni.clearStorageSync();
        uni.showToast({ title: '数据已重置', icon: 'success' });
        uni.reLaunch({
          url: '/pages/enter/enter',
        });
      }
    },
  });
};

// 组件挂载时加载通知设置
onMounted(() => {
  loadNotificationSetting();
});
</script>

<style lang="scss">
// 使用全局现代化主题样式
.bottom-decoration {
  height: 100rpx;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.1), transparent);
  position: relative;
  z-index: 1;
}

// 设置卡片样式
.settings-card {
  .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;

    .header-icon {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      background: var(--profile-gradient);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16rpx;

      .icon-text {
        font-size: 24rpx;
        color: white;
      }
    }

    .card-title {
      font-size: 28rpx;
      font-weight: 600;
      color: var(--gray-800);
    }
  }

  .settings-list {
    .setting-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 24rpx 0;
      border-bottom: 1rpx solid var(--gray-200);
      transition: all 0.3s ease;

      &:last-child {
        border-bottom: none;
      }

      &.clickable:active {
        background: rgba(255, 255, 255, 0.5);
        border-radius: var(--radius-lg);
      }

      &.danger {
        border-bottom-color: rgba(255, 59, 48, 0.2);

        &:last-child {
          border-bottom: none;
        }
      }

      .setting-info {
        display: flex;
        align-items: center;
        flex: 1;

        .setting-icon {
          width: 50rpx;
          height: 50rpx;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16rpx;

          &.notification {
            background: var(--checkin-gradient);
          }

          &.update {
            background: var(--plan-gradient);
          }

          &.feedback {
            background: var(--social-gradient);
          }

          &.reset {
            background: linear-gradient(135deg, #ff3b30, #ff6b6b);
          }

          .icon-text {
            font-size: 20rpx;
            color: white;
          }
        }

        .setting-content {
          flex: 1;

          .setting-label {
            font-size: 26rpx;
            font-weight: 600;
            color: var(--gray-800);
            margin-bottom: 4rpx;
            display: block;

            &.danger-text {
              color: #ff3b30;
            }
          }

          .setting-desc {
            font-size: 22rpx;
            color: var(--gray-500);
            line-height: 1.3;

            &.danger-desc {
              color: rgba(255, 59, 48, 0.7);
            }
          }
        }
      }

      .modern-switch {
        transform: scale(0.9);
      }

      .setting-arrow {
        font-size: 32rpx;
        color: var(--gray-400);
        font-weight: 300;

        &.danger-arrow {
          color: rgba(255, 59, 48, 0.6);
        }
      }
    }
  }
}
</style>