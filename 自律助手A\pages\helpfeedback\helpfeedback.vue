<template>
  <view class="feedback-page">
    <!-- 标题 -->
    <view class="section-title">
      <text>评价与反馈</text>
    </view>

    <!-- 评分区域 -->
    <view class="rating-section">
      <view class="rating-title">请给我们评分</view>
      <view class="stars-container">
        <text 
          v-for="index in 5" 
          :key="index" 
          class="star" 
          :class="{ 'active': index <= rating }" 
          @click="setRating(index)"
        >★</text>
      </view>
    </view>

    <!-- 意见提交区域 -->
    <view class="feedback-section">
      <textarea 
        v-model="feedbackText" 
        class="feedback-input" 
        placeholder="请输入您的宝贵意见..." 
      />
      <button class="submit-button" @click="submitFeedback">
        <text class="button-text">提交反馈</text>
      </button>
    </view>

    <!-- 历史记录区域 -->
    <view class="history-section">
      <view class="section-title">
        <text>反馈历史</text>
      </view>
      <view 
        v-for="(record, index) in feedbackRecords" 
        :key="index" 
        class="history-item"
      >
        <view class="history-header" @click="toggleRecord(index)">
          <text>反馈 #{{ index + 1 }} - {{ record.time }}</text>
          <text class="toggle-icon">{{ record.expanded ? '▲' : '▼' }}</text>
        </view>
        <view v-if="record.expanded" class="history-content" @click="showRecordDetail(record)">
          <text>评分: {{ record.rating }} 星</text>
          <text>内容: {{ record.text }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

const rating = ref(0);
const feedbackText = ref('');
const feedbackRecords = ref([]);

// 加载本地存储的数据
const loadData = () => {
  try {
    const storedRating = uni.getStorageSync('rating');
    const storedRecords = uni.getStorageSync('feedbackRecords');
    
    // 加载评分
    if (storedRating) {
      rating.value = Number(storedRating);
    }
    
    // 加载反馈记录，确保是数组
    if (storedRecords) {
      const parsedRecords = JSON.parse(storedRecords);
      if (Array.isArray(parsedRecords)) {
        feedbackRecords.value = parsedRecords;
      } else {
        feedbackRecords.value = [];
      }
    } else {
      feedbackRecords.value = [];
    }
  } catch (e) {
    console.error('加载数据失败', e);
    feedbackRecords.value = []; // 出错时初始化为空数组
    rating.value = 0; // 出错时初始化为0
  }
};

// 保存数据到本地
const saveData = () => {
  try {
    uni.setStorageSync('rating', rating.value.toString());
    uni.setStorageSync('feedbackRecords', JSON.stringify(feedbackRecords.value));
  } catch (e) {
    console.error('保存数据失败', e);
  }
};

// 设置评分
const setRating = (value) => {
  rating.value = value;
  saveData();
};

// 提交反馈
const submitFeedback = () => {
  if (!feedbackText.value.trim()) {
    uni.showToast({ title: '请输入反馈内容', icon: 'none' });
    return;
  }
  
  const newRecord = {
    rating: rating.value,
    text: feedbackText.value,
    time: new Date().toLocaleString(),
    expanded: false
  };
  
  feedbackRecords.value.unshift(newRecord);
  feedbackText.value = '';
  saveData();
  uni.showToast({ title: '提交成功', icon: 'success' });
};

// 切换记录展开状态
const toggleRecord = (index) => {
  // 检查记录是否存在
  if (feedbackRecords.value[index]) {
    feedbackRecords.value[index].expanded = !feedbackRecords.value[index].expanded;
    saveData();
  }
};

// 显示记录详情弹窗
const showRecordDetail = (record) => {
  uni.showModal({
    title: `反馈详情 - ${record.time}`,
    content: `评分: ${record.rating} 星\n内容: ${record.text}`,
    showCancel: false,
    confirmText: '关闭'
  });
};

// 页面加载时初始化数据
onMounted(() => {
  loadData();
});
</script>

<style lang="scss">
/* 样式保持不变 */
.feedback-page {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.section-title {
  margin-bottom: 30rpx;
  text {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
  }
}

.rating-section {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  
  .rating-title {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 20rpx;
  }
  
  .stars-container {
    display: flex;
    justify-content: center;
    .star {
      font-size: 60rpx;
      color: #ccc;
      margin: 0 10rpx;
      &.active {
        color: #ffd700;
      }
    }
  }
}

.feedback-section {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  
  .feedback-input {
    width: 100%;
    height: 200rpx;
    border: 2rpx solid #eee;
    border-radius: 10rpx;
    padding: 20rpx;
    font-size: 28rpx;
    margin-bottom: 30rpx;
  }
}

.submit-button {
  width: 100%;
  height: 100rpx;
  background-color: #fff;
  border: 2rpx solid #007aff;
  border-radius: 20rpx;
  color: #333;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s;
  
  .button-text {
    flex: 1;
    text-align: center;
  }
  
  &:active {
    background-color: #f0f8ff;
  }
}

.history-section {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  
  .history-item {
    margin-bottom: 20rpx;
    &:last-child {
      margin-bottom: 0;
    }
    
    .history-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx 0;
      border-bottom: 1rpx solid #eee;
      
      text {
        font-size: 28rpx;
        color: #333;
      }
      
      .toggle-icon {
        font-size: 24rpx;
        color: #666;
      }
    }
    
    .history-content {
      padding: 20rpx;
      background-color: #f9f9f9;
      border-radius: 10rpx;
      margin-top: 10rpx;
      
      text {
        display: block;
        font-size: 26rpx;
        color: #666;
        margin-bottom: 10rpx;
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>