<script>
	export default {
		onLaunch: function() {
			console.log('App Launch')
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		}
	}
</script>

<style lang="scss">
	/*每个页面公共css */
	@import './common/styles/modern-theme.scss';

	/* 全局重置样式 */
	* {
		box-sizing: border-box;
	}

	page {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
	}

	/* 导航栏样式优化 */
	.uni-page-head {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
		backdrop-filter: blur(20rpx) !important;
		border-bottom: none !important;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1) !important;
	}

	/* TabBar简洁样式 */
	uni-tabbar {
		background: rgba(255, 255, 255, 0.98) !important;
		border-top: 1rpx solid rgba(0, 0, 0, 0.1) !important;
		box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1) !important;
	}

	/* 统一滚动条样式 */
	::-webkit-scrollbar {
		width: 6rpx;
		height: 6rpx;
	}

	::-webkit-scrollbar-track {
		background: rgba(255, 255, 255, 0.1);
		border-radius: 3rpx;
	}

	::-webkit-scrollbar-thumb {
		background: rgba(255, 255, 255, 0.3);
		border-radius: 3rpx;
	}

	::-webkit-scrollbar-thumb:hover {
		background: rgba(255, 255, 255, 0.5);
	}
</style>
