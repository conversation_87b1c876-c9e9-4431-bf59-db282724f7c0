<script>
	export default {
		onLaunch: function() {
			console.log('App Launch')
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		}
	}
</script>

<style lang="scss">
	/*每个页面公共css */
	@import './common/styles/modern-theme.scss';
	@import './common/styles/android-compatibility.scss';

	/* 全局重置样式 */
	* {
		box-sizing: border-box;
	}

	page {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
	}

	/* 导航栏样式优化 */
	.uni-page-head {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
		backdrop-filter: blur(20rpx) !important;
		border-bottom: none !important;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1) !important;
	}

	/* TabBar简洁样式 - 安卓兼容性优化 */
	uni-tabbar {
		background: #ffffff !important;
		border-top: 1px solid #e5e5e5 !important;
		box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1) !important;
		height: 50px !important;
		position: fixed !important;
		bottom: 0 !important;
		left: 0 !important;
		right: 0 !important;
		z-index: 1000 !important;
	}

	/* TabBar项目样式 */
	uni-tabbar .uni-tabbar__item {
		display: flex !important;
		flex-direction: column !important;
		align-items: center !important;
		justify-content: center !important;
		height: 50px !important;
		padding: 4px 0 !important;
	}

	/* TabBar图标样式 */
	uni-tabbar .uni-tabbar__icon {
		width: 24px !important;
		height: 24px !important;
		margin-bottom: 2px !important;
	}

	/* TabBar文字样式 */
	uni-tabbar .uni-tabbar__text {
		font-size: 10px !important;
		line-height: 1 !important;
		color: #8a8a8a !important;
	}

	/* TabBar选中状态 */
	uni-tabbar .uni-tabbar__item.uni-tabbar__item--selected .uni-tabbar__text {
		color: #667eea !important;
	}

	/* 统一滚动条样式 */
	::-webkit-scrollbar {
		width: 6rpx;
		height: 6rpx;
	}

	::-webkit-scrollbar-track {
		background: rgba(255, 255, 255, 0.1);
		border-radius: 3rpx;
	}

	::-webkit-scrollbar-thumb {
		background: rgba(255, 255, 255, 0.3);
		border-radius: 3rpx;
	}

	::-webkit-scrollbar-thumb:hover {
		background: rgba(255, 255, 255, 0.5);
	}
</style>
