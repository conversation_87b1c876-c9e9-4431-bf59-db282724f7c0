<template>
  <view class="modern-page">
    <!-- 头部区域 -->
    <view class="modern-header">
      <text class="header-title">智能计划</text>
      <text class="header-subtitle">科学规划，高效执行</text>
      <view class="header-decoration"></view>
    </view>

    <!-- 内容区域 -->
    <view class="modern-content">
      <view class="modern-grid">
        <!-- 计划管理卡片 -->
        <navigator url="/pages/planmanagement/planmanagement" class="feature-card-nav">
          <view class="feature-card plan-card fade-in-up">
            <view class="card-icon">
              <text class="icon-text">📋</text>
            </view>
            <view class="card-content">
              <text class="card-title">计划管理</text>
              <text class="card-desc">创建和管理学习计划</text>
            </view>
            <view class="card-arrow">
              <text class="arrow-icon">→</text>
            </view>
          </view>
        </navigator>

        <!-- 时间提醒卡片 -->
        <navigator url="/pages/timereminder/timereminder" class="feature-card-nav">
          <view class="feature-card monitor-card fade-in-up">
            <view class="card-icon">
              <text class="icon-text">⏰</text>
            </view>
            <view class="card-content">
              <text class="card-title">时间提醒</text>
              <text class="card-desc">智能提醒，不错过任何计划</text>
            </view>
            <view class="card-arrow">
              <text class="arrow-icon">→</text>
            </view>
          </view>
        </navigator>
      </view>
    </view>

    <!-- 底部装饰 -->
    <view class="bottom-decoration"></view>
  </view>
</template>

<script setup>
// 可以在这里添加逻辑代码
</script>

<style lang="scss">
// 使用全局现代化主题样式
.feature-card-nav {
  text-decoration: none;
}

.bottom-decoration {
  height: 100rpx;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.1), transparent);
  position: relative;
  z-index: 1;
}
</style>